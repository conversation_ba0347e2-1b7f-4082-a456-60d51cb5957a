# BKlight Mock Agent 完整使用指南

## 📖 目录

1. [概述与新特性](#概述与新特性)
2. [智能功能总结](#智能功能总结)
3. [基础使用指南](#基础使用指南)
4. [智能交互API](#智能交互api)
5. [交互示例](#交互示例)
6. [大模型配置指南](#大模型配置指南)
7. [最佳实践](#最佳实践)

---

## 概述与新特性

BKlight Mock Agent 是一个基于MCP (Model Context Protocol) 协议的智能代理系统，能够串联多个工具来自动化执行BKlight Mock测试流程。该系统支持上下文感知、智能步骤管理和异常处理，能够根据执行状态智能决定下一步操作。

### 🆕 核心特性

- **上下文感知**: 系统记住用户的执行历史和当前状态
- **智能步骤管理**: 根据执行情况自动决定重试或继续
- **异常处理**: 详细的错误分析和处理建议
- **等待状态管理**: 智能处理异步操作的等待状态
- **🔥 智能意图分析**: 使用大模型分析用户输入，支持多样化表达
- **自然语言交互**: 支持中英文自然语言交互，理解复杂和模糊输入
- **全局配置管理**: 支持动态配置大模型参数

### 架构设计

#### 核心组件

1. **MCPServerBKlightMockTools** - 工具注册类，包含5个核心工具
2. **BklightMockAgentService** - Agent服务类，提供工具串联能力
3. **BklightMockAgentController** - REST API控制器
4. **BklightAgentConfig** - 全局配置管理类

#### 工具列表

| 工具名称 | 描述 | 输入参数 | 输出 |
|---------|------|---------|------|
| `addNewPlan` | 添加新的测试计划规则 | caseId, caseName | newPlanRuleId |
| `triggerEte` | 触发ETE执行 | caseId | 执行状态 |
| `queryNewPlanList` | 查询测新流量列表 | caseId, newPlanRuleId | caseInstanceId |
| `addDependencyRule` | 添加依赖规则并激活 | caseId, caseInstanceId, downStreamInjectType, relatedMachineGroup | 创建状态 |
| `queryInjectResultList` | 查询异常注入执行结果 | dependencyRuleId | 成功注入结果列表 |

---

## 智能功能总结

### 🎯 核心优化成果

#### 1. 智能意图分析系统

**技术实现**
- **大模型驱动**: 使用Qwen3-235B-A22B进行意图分析
- **上下文感知**: 结合用户执行状态进行智能判断
- **多语言支持**: 支持中英文及混合表达
- **智能回退**: 大模型不可用时自动回退到规则匹配

**支持的意图类别**
```
start_new_flow    - 开始新的测试流程
continue_flow     - 继续现有流程
check_status      - 查看执行状态
retry_step[:N]    - 重试特定步骤
wait_check        - 检查等待中的注入
clear_restart     - 清除上下文重新开始
help_info         - 获取帮助信息
general_query     - 一般查询
```

**处理能力**
- ✅ 多样化表达：同一意图的不同表达方式
- ✅ 复杂语句：理解复杂和模糊的用户输入
- ✅ 情感识别：识别用户情感状态并给出合适回应
- ✅ 多重意图：处理包含多个条件的复杂表达
- ✅ 技术术语：理解专业术语和缩写

#### 2. 上下文感知执行

**执行状态管理**
```java
public static class ExecutionContext {
    private String caseId, caseName, downStreamInjectType, relatedMachineGroup;
    private String newPlanRuleId, caseInstanceId, dependencyRuleId;
    private int currentStep = 0;
    private boolean step1Completed, step2Completed, step3Completed, 
                   step4Completed, step5Completed;
    private boolean hasWaitingInjections;
    private long lastWaitPromptTime;
}
```

**智能决策逻辑**
- **首次使用**: 执行完整流程（步骤1-5）
- **步骤失败**: 从失败步骤重新开始
- **等待状态**: 智能管理异步操作等待
- **循环重试**: 步骤2-3循环直到找到完整依赖树

#### 3. 异常处理优化

**详细的步骤验证**
```
步骤1: newPlanRuleId != null
步骤2: eteExecuteSuccess == true
步骤3: flowFieldCount > 0 (preferably > 10)
步骤4: addDependencyStatus == true
步骤5: 复杂的注入状态分析
```

**注入结果智能分析**
```
1. 全部WAIT: "注入尚未开始，需要再等待10-30分钟"
2. WAIT > SUCCESS+FAIL: "需要再等待10-30分钟"
3. 全部FAIL: "注入失败，需bklight人员排查"
4. SUCCESS > FAIL+WAIT: 返回详细统计和建议
```

---

## 基础使用指南

### 智能执行流程

#### 执行逻辑

系统根据以下逻辑智能决定执行步骤：

1. **首次使用**: 如果上下文中不存在任何步骤记录，执行完整流程（步骤1-5）
2. **步骤失败重试**: 如果某个步骤失败，从该步骤重新开始执行
3. **等待状态处理**: 对于异步操作，智能管理等待状态和重试时机
4. **上下文继承**: 保持用户会话状态，支持分步骤交互

#### 详细步骤说明

**步骤1**: 增加测新规则
- 成功条件: `newPlanRuleId` 不为null
- 失败处理: 返回详细错误原因

**步骤2**: 触发ETE执行
- 成功条件: `eteExecuteSuccess` 为true
- 失败处理: 返回详细错误原因

**步骤3**: 查询测新流量列表
- 成功条件: 找到`flowFieldCount > 0`（最好>10）的`caseInstanceId`
- 失败处理: 循环执行步骤2-3直到找到完整依赖树

**步骤4**: 新增依赖配置规则
- 成功条件: `addDependencyStatus` 为true
- 失败处理: 返回详细错误原因

**步骤5**: 查询异常注入执行结果
- 全部WAIT: "注入尚未开始，需要再等待10-30分钟再单独执行该步骤"
- WAIT多于SUCCESS+FAIL: "需要再等待10-30分钟再单独执行该步骤"
- 全部FAIL: "注入失败，需bklight人员排查"
- SUCCESS多于FAIL+WAIT: 返回详细统计，对WAIT状态提示等待

**步骤6**: 等待后重新检查
- 针对步骤5中有WAIT状态的情况，等待后重新执行检查

### 🔥 智能意图分析

#### 工作原理

系统使用大模型（Qwen3-235B-A22B）来分析用户输入的意图，支持：

1. **多样化表达**: 理解同一意图的不同表达方式
2. **上下文感知**: 结合用户当前执行状态分析意图
3. **多语言支持**: 支持中英文混合输入
4. **复杂语句**: 理解复杂和模糊的用户输入
5. **智能回退**: 当大模型不可用时，自动回退到规则匹配

#### 支持的意图类别

| 意图类别 | 描述 | 示例输入 |
|---------|------|---------|
| `start_new_flow` | 开始新的测试流程 | "开始执行测试"、"start testing"、"启动异常注入" |
| `continue_flow` | 继续现有流程 | "继续执行"、"continue"、"接着上次的进度" |
| `check_status` | 查看执行状态 | "查看状态"、"show status"、"现在进行到哪一步了？" |
| `retry_step` | 重试特定步骤 | "重试步骤3"、"retry step 2"、"重新执行第一步" |
| `wait_check` | 检查等待中的注入 | "检查注入结果"、"查看WAIT状态"、"注入完成了吗？" |
| `clear_restart` | 清除上下文重新开始 | "重新开始"、"clear and restart"、"重置所有状态" |
| `help_info` | 获取帮助信息 | "帮助"、"help"、"怎么使用？" |
| `general_query` | 一般查询 | 其他未明确分类的输入 |

---

## 智能交互API

### 🚀 新的统一智能接口

#### API端点
```
POST /api/bklight/agent/smart-interact
```

#### 核心特性
- ✅ **参数自动检查**: 自动验证必填参数并友好提示
- ✅ **意图智能识别**: 使用大模型分析用户输入意图
- ✅ **步骤顺序验证**: 自动检查步骤依赖关系并纠正
- ✅ **上下文管理**: 智能管理用户会话状态
- ✅ **自然语言交互**: 支持多样化表达方式

#### 请求格式
```json
{
  "userId": "user123",
  "userInput": "开始执行BKlight Mock测试",
  "caseId": "a9cd8ba6-f714-41ab-bad0-4022a2f60126",
  "caseName": "[余额宝][RPC]余额宝转出页渲染",
  "downStreamInjectType": "PRC_TIME_OUT_EXCEPTION",
  "relatedMachineGroup": "GROUP_20250630104315"
}
```

### 使用示例

#### 示例1：首次使用 - 完整参数

```bash
curl -X POST "http://localhost:8080/api/bklight/agent/smart-interact" \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "user123",
    "userInput": "开始执行BKlight Mock测试",
    "caseId": "a9cd8ba6-f714-41ab-bad0-4022a2f60126",
    "caseName": "[余额宝][RPC]余额宝转出页渲染",
    "downStreamInjectType": "PRC_TIME_OUT_EXCEPTION",
    "relatedMachineGroup": "GROUP_20250630104315"
  }'
```

#### 示例2：首次使用 - 缺少参数

```bash
curl -X POST "http://localhost:8080/api/bklight/agent/smart-interact" \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "user123",
    "userInput": "开始执行测试"
  }'
```

**系统响应**:
```
🔧 **参数检查**

检测到您想要执行BKlight Mock测试，但缺少以下必填参数：

- caseId (用例ID)
- caseName (用例名称)
- downStreamInjectType (异常类型，如：PRC_TIME_OUT_EXCEPTION)
- relatedMachineGroup (机器组，如：GROUP_20250630104315)

请提供完整的参数信息...
```

#### 示例3：步骤顺序自动纠正

```bash
curl -X POST "http://localhost:8080/api/bklight/agent/smart-interact" \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "user123",
    "userInput": "我想执行步骤3查询测新流量列表"
  }'
```

**系统响应**（假设步骤1未完成）:
```
⚠️ **步骤顺序检查**

检测到您想执行步骤3（查询测新流量列表），但前置步骤尚未完成。

**自动处理：**系统将自动从步骤1开始执行，确保流程的正确性。

**执行顺序：**
1. ✅ 首先执行步骤1：创建测新规则
2. ➡️ 然后执行步骤2：触发ETE执行
3. ➡️ 最后执行步骤3：查询测新流量列表

正在开始执行...
```

### 🔧 兼容性接口

#### 原有smart-chat接口（保持兼容）

```bash
curl -X POST "http://localhost:8080/api/bklight/agent/smart-chat?userId=user123&userInput=开始执行测试"
```

#### 功能对比

| 功能特性 | smart-interact | smart-chat | 说明 |
|---------|---------------|------------|------|
| 参数自动检查 | ✅ | ❌ | 新接口自动验证必填参数 |
| 步骤顺序验证 | ✅ | ❌ | 新接口自动检查步骤依赖 |
| 意图智能识别 | ✅ | ✅ | 都支持大模型意图分析 |
| 上下文管理 | ✅ | ✅ | 都支持会话状态管理 |
| 参数传递方式 | JSON Body | Query Params | 新接口支持结构化参数 |
| 错误提示 | 友好详细 | 基础提示 | 新接口提供更好的用户体验 |

---

## 交互示例

### 🔥 智能意图分析展示

#### 多样化表达识别

**开始执行的不同表达方式**
```bash
# 正式表达
"开始执行BKlight Mock测试"

# 简洁表达
"开始吧"

# 英文表达
"start testing"

# 口语化表达
"我想开始测试"
```

**系统分析结果**: 所有输入都被识别为 `start_new_flow` 意图

**状态查询的不同表达方式**
```bash
# 直接询问
"查看当前执行状态"

# 疑问句
"现在进行到哪一步了？"

# 英文表达
"what's the current progress"

# 简短表达
"状态"
```

**系统分析结果**: 所有输入都被识别为 `check_status` 意图

#### 复杂语句理解

**复杂意图表达**
```bash
用户输入: "我想看看步骤3是否执行成功了，如果失败了就重试"
```

**系统分析过程**:
1. 大模型识别关键信息：步骤3、检查、重试
2. 结合上下文：用户当前执行状态
3. 分析结果：`retry_step:3`
4. 系统响应：检查步骤3状态并重试

**模糊表达处理**
```bash
用户输入: "刚才的测试好像有问题，我想重新开始整个流程"
```

**系统分析过程**:
1. 识别问题描述：测试有问题
2. 识别用户意图：重新开始整个流程
3. 分析结果：`clear_restart`
4. 系统响应：清除上下文，准备新流程

### 场景示例

#### 场景1：首次执行完整流程

```bash
curl -X POST "http://localhost:8080/api/bklight/agent/smart-interact" \
  -d '{
    "userId": "user001",
    "userInput": "开始执行BKlight Mock测试，用例ID是a9cd8ba6-f714-41ab-bad0-4022a2f60126",
    "caseId": "a9cd8ba6-f714-41ab-bad0-4022a2f60126",
    "caseName": "[余额宝][RPC]余额宝转出页渲染",
    "downStreamInjectType": "PRC_TIME_OUT_EXCEPTION",
    "relatedMachineGroup": "GROUP_20250630104315"
  }'
```

**Agent响应**:
```
检测到这是您的首次执行，将开始完整的BKlight Mock测试流程。

步骤1：正在添加测新规则...
✓ 成功创建测新规则，newPlanRuleId: PLAN1749785601048

步骤2：正在触发ETE执行...
✓ ETE执行触发成功

步骤3：正在查询测新流量列表...
✓ 找到合适的caseInstanceId: d9c205c6-9afe-470e-a831-e672252a59a1 (flowFieldCount: 15)

步骤4：正在添加依赖配置规则...
✓ 依赖规则创建成功

步骤5：正在查询异常注入执行结果...
当前注入状态：
- WAIT: 8个 (66.7%)
- SUCCESS: 2个 (16.7%)
- FAIL: 2个 (16.7%)

由于还有等待中的注入，建议您等待10-30分钟后再次查询结果。
```

#### 场景2：步骤失败重试

```bash
curl -X POST "http://localhost:8080/api/bklight/agent/smart-interact" \
  -d '{
    "userId": "user001",
    "userInput": "重试步骤2"
  }'
```

**Agent响应**:
```
检测到步骤2之前执行失败，正在重新执行步骤2...

步骤2：正在触发ETE执行...
✓ ETE执行触发成功

继续执行后续步骤...
步骤3：正在查询测新流量列表...
✓ 找到合适的caseInstanceId: d9c205c6-9afe-470e-a831-e672252a59a1

步骤4：正在添加依赖配置规则...
✓ 依赖规则创建成功

步骤5：正在查询异常注入执行结果...
[结果详情...]
```

#### 场景3：等待后重新检查

```bash
curl -X POST "http://localhost:8080/api/bklight/agent/smart-interact" \
  -d '{
    "userId": "user001",
    "userInput": "检查注入结果"
  }'
```

**Agent响应**（30分钟后）:
```
正在重新检查异常注入执行结果...

步骤6：查询最新注入状态...
✓ 注入执行完成！

最终结果统计：
- SUCCESS: 10个 (83.3%) ✓
- FAIL: 2个 (16.7%) ✗
- WAIT: 0个 (0%)

成功注入的用例：
1. [用例A] - 超时异常注入成功
2. [用例B] - 网络异常注入成功
...

失败注入的用例：
1. [用例X] - 注入失败，原因：目标服务不可达
2. [用例Y] - 注入失败，原因：权限不足

BKlight Mock测试流程已完成！
```

---

## 大模型配置指南

### 🎯 配置概述

BKlight Mock Agent 现在支持全局配置大模型参数，包括模型名称、温度设置等，方便在不同环境中灵活配置。

### 📝 配置方式

#### 1. 配置文件方式

**application.properties**
```properties
# 基础配置
bklight.agent.llm.model=Qwen3-235B-A22B
bklight.agent.llm.temperature=0.1
bklight.agent.llm.intent-analysis.temperature=0.1
bklight.agent.llm.max-retry=3
bklight.agent.llm.timeout=30

# 监控和日志
bklight.agent.monitoring.enabled=true
bklight.agent.logging.llm-calls=false
bklight.agent.logging.intent-analysis=false
bklight.agent.metrics.enabled=true
```

**application-dev.properties (开发环境)**
```properties
bklight.agent.llm.model=Qwen3-72B-A14B
bklight.agent.llm.temperature=0.2
bklight.agent.llm.intent-analysis.temperature=0.1
bklight.agent.logging.llm-calls=true
bklight.agent.logging.intent-analysis=true
```

**application-prod.properties (生产环境)**
```properties
bklight.agent.llm.model=Qwen3-235B-A22B
bklight.agent.llm.temperature=0.1
bklight.agent.llm.intent-analysis.temperature=0.05
bklight.agent.llm.max-retry=5
bklight.agent.llm.timeout=60
```

#### 2. 环境变量方式

```bash
# 设置模型名称
export BKLIGHT_AGENT_LLM_MODEL=GPT-4

# 设置温度
export BKLIGHT_AGENT_LLM_TEMPERATURE=0.2

# 设置意图分析温度
export BKLIGHT_AGENT_LLM_INTENT_ANALYSIS_TEMPERATURE=0.1
```

#### 3. JVM参数方式

```bash
java -jar app.jar \
  -Dbklight.agent.llm.model=Claude-3 \
  -Dbklight.agent.llm.temperature=0.15 \
  -Dbklight.agent.llm.intent-analysis.temperature=0.05
```

### 🔧 动态配置管理

#### 查看当前配置

```bash
# 获取完整配置
curl -X GET "http://localhost:8080/api/bklight/agent/config/current"

# 获取配置摘要
curl -X GET "http://localhost:8080/api/bklight/agent/config/summary"

# 验证配置有效性
curl -X GET "http://localhost:8080/api/bklight/agent/config/validate"
```

#### 动态修改配置

```bash
# 更新模型名称
curl -X POST "http://localhost:8080/api/bklight/agent/config/llm/model?model=GPT-4"

# 更新LLM温度
curl -X POST "http://localhost:8080/api/bklight/agent/config/llm/temperature?temperature=0.2"

# 更新意图分析温度
curl -X POST "http://localhost:8080/api/bklight/agent/config/intent-analysis/temperature?temperature=0.05"

# 切换监控状态
curl -X POST "http://localhost:8080/api/bklight/agent/config/monitoring/toggle"
```

#### 预设配置

```bash
# 重置为生产环境配置
curl -X POST "http://localhost:8080/api/bklight/agent/config/reset/production"

# 重置为开发环境配置
curl -X POST "http://localhost:8080/api/bklight/agent/config/reset/development"
```

### 🎨 模型选择指南

#### 推荐模型配置

| 环境 | 模型 | 温度 | 意图分析温度 | 适用场景 |
|------|------|------|-------------|----------|
| 生产环境 | Qwen3-235B-A22B | 0.1 | 0.05 | 高准确性要求 |
| 测试环境 | Qwen3-72B-A14B | 0.15 | 0.1 | 平衡性能和准确性 |
| 开发环境 | Qwen3-72B-A14B | 0.2 | 0.1 | 快速迭代开发 |
| 演示环境 | Qwen3-235B-A22B | 0.3 | 0.1 | 展示效果 |

#### 模型特性对比

| 模型 | 响应速度 | 准确性 | 成本 | 推荐用途 |
|------|---------|--------|------|----------|
| Qwen3-235B-A22B | 中等 | 极高 | 高 | 生产环境 |
| Qwen3-72B-A14B | 快 | 高 | 中 | 开发测试 |
| GPT-4 | 中等 | 极高 | 高 | 特殊需求 |
| Claude-3 | 中等 | 极高 | 高 | 特殊需求 |

### 🌡️ 温度参数调优

#### 温度设置建议

| 用途 | 推荐温度 | 说明 |
|------|---------|------|
| 意图分析 | 0.0 - 0.1 | 需要高度确定性 |
| 工具调用 | 0.1 - 0.2 | 平衡准确性和灵活性 |
| 对话生成 | 0.2 - 0.5 | 自然对话体验 |
| 创意生成 | 0.5 - 1.0 | 需要创造性 |

---

## 最佳实践

### 🎯 使用建议

#### 1. 首次使用
- 推荐使用`smart-interact`接口
- 在JSON中提供完整的必填参数
- 使用清晰的userInput描述意图

#### 2. 后续交互
- 可以只提供userId和userInput
- 系统会自动使用之前保存的参数
- 支持自然语言描述各种操作需求

#### 3. 错误处理
- 系统会自动检查参数完整性
- 自动验证步骤执行顺序
- 提供详细的错误信息和解决建议

#### 4. 状态管理
- 定期使用"查看状态"检查进度
- 根据系统建议进行后续操作
- 必要时使用"重新开始"清除状态

### 🚀 高级用法

#### 批量操作
```bash
# 一次性提供所有参数并开始执行
curl -X POST "http://localhost:8080/api/bklight/agent/smart-interact" \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "user123",
    "userInput": "执行完整的BKlight Mock测试流程，包括所有5个步骤",
    "caseId": "test-case-001",
    "caseName": "完整测试用例",
    "downStreamInjectType": "PRC_TIME_OUT_EXCEPTION",
    "relatedMachineGroup": "GROUP_TEST"
  }'
```

#### 条件执行
```bash
# 复杂的条件表达
curl -X POST "http://localhost:8080/api/bklight/agent/smart-interact" \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "user123",
    "userInput": "如果步骤3失败了就重试，如果成功了就继续执行步骤4和5"
  }'
```

### 📊 监控和调试

#### 启用详细日志

```properties
# 启用LLM调用日志
bklight.agent.logging.llm-calls=true

# 启用意图分析日志
bklight.agent.logging.intent-analysis=true

# 设置日志级别
logging.level.com.alipay.findataquality.service.ai=DEBUG
```

#### 查看运行时指标

```bash
# 查看配置状态
curl -X GET "http://localhost:8080/api/bklight/agent/config/summary"

# 查看系统健康状态
curl -X GET "http://localhost:8080/actuator/health"

# 查看指标信息
curl -X GET "http://localhost:8080/actuator/metrics"
```

### 🔍 故障排查

#### 常见问题

1. **模型调用失败**
   ```bash
   # 检查配置
   curl -X GET "http://localhost:8080/api/bklight/agent/config/validate"
   
   # 检查模型名称是否正确
   curl -X GET "http://localhost:8080/api/bklight/agent/config/current"
   ```

2. **意图识别不准确**
   ```bash
   # 降低意图分析温度
   curl -X POST "http://localhost:8080/api/bklight/agent/config/intent-analysis/temperature?temperature=0.05"
   ```

3. **响应速度慢**
   ```bash
   # 切换到更快的模型
   curl -X POST "http://localhost:8080/api/bklight/agent/config/llm/model?model=Qwen3-72B-A14B"
   ```

### 🎉 用户体验提升

#### 交互自然度
- **多样化表达**: 用户可以用自然语言表达意图
- **容错能力**: 理解模糊和不完整的输入
- **智能提示**: 根据上下文提供合适的建议

#### 操作便利性
- **一键执行**: 智能判断需要执行的步骤
- **状态透明**: 清晰的执行状态反馈
- **错误友好**: 详细的错误信息和解决建议

#### 功能完整性
- **全流程覆盖**: 支持完整的BKlight Mock测试流程
- **异常处理**: 完善的异常情况处理机制
- **扩展性**: 易于添加新的工具和功能

---

## 🔮 未来扩展方向

1. **更多意图类别**: 支持更细粒度的操作意图
2. **个性化学习**: 学习用户习惯，提供个性化服务
3. **多模态交互**: 支持语音、图像等多种交互方式
4. **智能推荐**: 基于历史数据推荐最佳执行策略
5. **实时监控**: 实时监控执行状态，主动提醒用户

---

## 📞 技术支持

如有问题或建议，请联系开发团队或查看项目文档。

这个智能Agent系统提供了完整的BKlight Mock测试自动化解决方案，支持自然语言交互、智能步骤管理和灵活的配置管理，大大提升了测试效率和用户体验。
