# BKlight Mock Agent 交互示例

## 场景1：首次执行完整流程

### 用户操作
```bash
curl -X POST "http://localhost:8080/api/bklight/agent/smart-chat" \
  -d "userId=user001&userInput=开始执行BKlight Mock测试，用例ID是a9cd8ba6-f714-41ab-bad0-4022a2f60126"
```

### Agent响应
```
检测到这是您的首次执行，将开始完整的BKlight Mock测试流程。

步骤1：正在添加测新规则...
✓ 成功创建测新规则，newPlanRuleId: PLAN1749785601048

步骤2：正在触发ETE执行...
✓ ETE执行触发成功

步骤3：正在查询测新流量列表...
✓ 找到合适的caseInstanceId: d9c205c6-9afe-470e-a831-e672252a59a1 (flowFieldCount: 15)

步骤4：正在添加依赖配置规则...
✓ 依赖规则创建成功

步骤5：正在查询异常注入执行结果...
当前注入状态：
- WAIT: 8个 (66.7%)
- SUCCESS: 2个 (16.7%)
- FAIL: 2个 (16.7%)

由于还有等待中的注入，建议您等待10-30分钟后再次查询结果。
```

## 场景2：步骤失败重试

### 用户操作
```bash
curl -X POST "http://localhost:8080/api/bklight/agent/smart-chat" \
  -d "userId=user001&userInput=重试步骤2"
```

### Agent响应
```
检测到步骤2之前执行失败，正在重新执行步骤2...

步骤2：正在触发ETE执行...
✓ ETE执行触发成功

继续执行后续步骤...
步骤3：正在查询测新流量列表...
✓ 找到合适的caseInstanceId: d9c205c6-9afe-470e-a831-e672252a59a1

步骤4：正在添加依赖配置规则...
✓ 依赖规则创建成功

步骤5：正在查询异常注入执行结果...
[结果详情...]
```

## 场景3：等待后重新检查

### 用户操作（30分钟后）
```bash
curl -X POST "http://localhost:8080/api/bklight/agent/smart-chat" \
  -d "userId=user001&userInput=检查注入结果"
```

### Agent响应
```
正在重新检查异常注入执行结果...

步骤6：查询最新注入状态...
✓ 注入执行完成！

最终结果统计：
- SUCCESS: 10个 (83.3%) ✓
- FAIL: 2个 (16.7%) ✗
- WAIT: 0个 (0%)

成功注入的用例：
1. [用例A] - 超时异常注入成功
2. [用例B] - 网络异常注入成功
...

失败注入的用例：
1. [用例X] - 注入失败，原因：目标服务不可达
2. [用例Y] - 注入失败，原因：权限不足

BKlight Mock测试流程已完成！
```

## 场景4：查看执行状态

### 用户操作
```bash
curl -X GET "http://localhost:8080/api/bklight/agent/status?userId=user001"
```

### Agent响应
```
Execution Status for User: user001
Current Step: 5
Step 1 (Add New Plan): ✓ Completed
Step 2 (Trigger ETE): ✓ Completed
Step 3 (Query Plan List): ✓ Completed
Step 4 (Add Dependency Rule): ✓ Completed
Step 5 (Query Inject Results): ✓ Completed
Has Waiting Injections: Yes (25 minutes ago)

建议：可以重新检查注入结果，等待时间已足够。
```

## 场景5：异常处理示例

### 步骤1失败
```
❌ 步骤1执行失败
错误原因：测新规则创建失败 - 用例ID不存在或格式错误
建议：请检查caseId格式是否正确，或联系管理员确认用例是否存在
```

### 步骤3循环重试
```
步骤3：查询测新流量列表...
⚠️ 未找到足够的依赖节点（当前最大flowFieldCount: 3）
正在重新触发ETE执行以生成更多流量...

步骤2：重新触发ETE执行...
✓ ETE执行触发成功

步骤3：再次查询测新流量列表...
✓ 找到合适的caseInstanceId: xxx (flowFieldCount: 12)
```

### 步骤5全部失败
```
❌ 步骤5执行失败
错误原因：所有异常注入都失败了
详细信息：
- 总计12个注入用例
- 失败12个 (100%)
- 主要失败原因：目标服务集群不可用

建议：注入失败，需要bklight人员排查服务可用性问题
```

## 场景6：自然语言交互

### 各种用户输入示例

```bash
# 开始执行
"开始BKlight测试"
"执行异常注入测试"
"start testing"

# 查看状态
"当前进度如何？"
"查看执行状态"
"show me the status"

# 重试操作
"重新执行步骤3"
"retry step 2"
"步骤4失败了，重试一下"

# 等待检查
"等待30分钟后检查结果"
"检查注入是否完成"
"查看最新的注入结果"

# 清除重新开始
"清除当前进度，重新开始"
"reset and start over"
```

## 场景7：API调用序列

### 完整交互流程
```bash
# 1. 开始执行
curl -X POST "http://localhost:8080/api/bklight/agent/smart-execute" \
  -H "Content-Type: application/json" \
  -d '{"userId":"user001","caseId":"test123","caseName":"测试用例",...}'

# 2. 检查状态
curl -X GET "http://localhost:8080/api/bklight/agent/status?userId=user001"

# 3. 如果有失败，重试特定步骤
curl -X POST "http://localhost:8080/api/bklight/agent/execute-specific-step?userId=user001&stepNumber=3"

# 4. 等待后重新检查
curl -X POST "http://localhost:8080/api/bklight/agent/smart-chat" \
  -d "userId=user001&userInput=检查注入结果"

# 5. 完成后清除上下文
curl -X POST "http://localhost:8080/api/bklight/agent/clear-context?userId=user001"
```

## 最佳实践

1. **使用智能接口**：优先使用 `/smart-execute` 和 `/smart-chat` 接口
2. **保持用户ID一致**：同一个测试流程使用相同的userId
3. **合理等待**：对于异步操作，遵循系统建议的等待时间
4. **错误处理**：根据错误提示进行相应的重试或修复操作
5. **状态检查**：定期使用 `/status` 接口检查执行进度
6. **上下文管理**：测试完成后及时清除上下文，避免干扰后续测试
