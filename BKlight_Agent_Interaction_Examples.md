# BKlight Mock Agent 交互示例

## 🔥 智能意图分析展示

### 多样化表达识别

#### 开始执行的不同表达方式
```bash
# 正式表达
curl -X POST "http://localhost:8080/api/bklight/agent/smart-chat" \
  -d "userId=user001&userInput=开始执行BKlight Mock测试"

# 简洁表达
curl -X POST "http://localhost:8080/api/bklight/agent/smart-chat" \
  -d "userId=user001&userInput=开始吧"

# 英文表达
curl -X POST "http://localhost:8080/api/bklight/agent/smart-chat" \
  -d "userId=user001&userInput=start testing"

# 口语化表达
curl -X POST "http://localhost:8080/api/bklight/agent/smart-chat" \
  -d "userId=user001&userInput=我想开始测试"
```

**系统分析结果**: 所有输入都被识别为 `start_new_flow` 意图

#### 状态查询的不同表达方式
```bash
# 直接询问
curl -X POST "http://localhost:8080/api/bklight/agent/smart-chat" \
  -d "userId=user001&userInput=查看当前执行状态"

# 疑问句
curl -X POST "http://localhost:8080/api/bklight/agent/smart-chat" \
  -d "userId=user001&userInput=现在进行到哪一步了？"

# 英文表达
curl -X POST "http://localhost:8080/api/bklight/agent/smart-chat" \
  -d "userId=user001&userInput=what's the current progress"

# 简短表达
curl -X POST "http://localhost:8080/api/bklight/agent/smart-chat" \
  -d "userId=user001&userInput=状态"
```

**系统分析结果**: 所有输入都被识别为 `check_status` 意图

### 复杂语句理解

#### 复杂意图表达
```bash
# 复杂条件句
curl -X POST "http://localhost:8080/api/bklight/agent/smart-chat" \
  -d "userId=user001&userInput=我想看看步骤3是否执行成功了，如果失败了就重试"
```

**系统分析过程**:
1. 大模型识别关键信息：步骤3、检查、重试
2. 结合上下文：用户当前执行状态
3. 分析结果：`retry_step:3`
4. 系统响应：检查步骤3状态并重试

#### 模糊表达处理
```bash
# 模糊的重新开始请求
curl -X POST "http://localhost:8080/api/bklight/agent/smart-chat" \
  -d "userId=user001&userInput=刚才的测试好像有问题，我想重新开始整个流程"
```

**系统分析过程**:
1. 识别问题描述：测试有问题
2. 识别用户意图：重新开始整个流程
3. 分析结果：`clear_restart`
4. 系统响应：清除上下文，准备新流程

## 场景1：首次执行完整流程

### 用户操作
```bash
curl -X POST "http://localhost:8080/api/bklight/agent/smart-chat" \
  -d "userId=user001&userInput=开始执行BKlight Mock测试，用例ID是a9cd8ba6-f714-41ab-bad0-4022a2f60126"
```

### Agent响应
```
检测到这是您的首次执行，将开始完整的BKlight Mock测试流程。

步骤1：正在添加测新规则...
✓ 成功创建测新规则，newPlanRuleId: PLAN1749785601048

步骤2：正在触发ETE执行...
✓ ETE执行触发成功

步骤3：正在查询测新流量列表...
✓ 找到合适的caseInstanceId: d9c205c6-9afe-470e-a831-e672252a59a1 (flowFieldCount: 15)

步骤4：正在添加依赖配置规则...
✓ 依赖规则创建成功

步骤5：正在查询异常注入执行结果...
当前注入状态：
- WAIT: 8个 (66.7%)
- SUCCESS: 2个 (16.7%)
- FAIL: 2个 (16.7%)

由于还有等待中的注入，建议您等待10-30分钟后再次查询结果。
```

## 场景2：步骤失败重试

### 用户操作
```bash
curl -X POST "http://localhost:8080/api/bklight/agent/smart-chat" \
  -d "userId=user001&userInput=重试步骤2"
```

### Agent响应
```
检测到步骤2之前执行失败，正在重新执行步骤2...

步骤2：正在触发ETE执行...
✓ ETE执行触发成功

继续执行后续步骤...
步骤3：正在查询测新流量列表...
✓ 找到合适的caseInstanceId: d9c205c6-9afe-470e-a831-e672252a59a1

步骤4：正在添加依赖配置规则...
✓ 依赖规则创建成功

步骤5：正在查询异常注入执行结果...
[结果详情...]
```

## 场景3：等待后重新检查

### 用户操作（30分钟后）
```bash
curl -X POST "http://localhost:8080/api/bklight/agent/smart-chat" \
  -d "userId=user001&userInput=检查注入结果"
```

### Agent响应
```
正在重新检查异常注入执行结果...

步骤6：查询最新注入状态...
✓ 注入执行完成！

最终结果统计：
- SUCCESS: 10个 (83.3%) ✓
- FAIL: 2个 (16.7%) ✗
- WAIT: 0个 (0%)

成功注入的用例：
1. [用例A] - 超时异常注入成功
2. [用例B] - 网络异常注入成功
...

失败注入的用例：
1. [用例X] - 注入失败，原因：目标服务不可达
2. [用例Y] - 注入失败，原因：权限不足

BKlight Mock测试流程已完成！
```

## 场景4：查看执行状态

### 用户操作
```bash
curl -X GET "http://localhost:8080/api/bklight/agent/status?userId=user001"
```

### Agent响应
```
Execution Status for User: user001
Current Step: 5
Step 1 (Add New Plan): ✓ Completed
Step 2 (Trigger ETE): ✓ Completed
Step 3 (Query Plan List): ✓ Completed
Step 4 (Add Dependency Rule): ✓ Completed
Step 5 (Query Inject Results): ✓ Completed
Has Waiting Injections: Yes (25 minutes ago)

建议：可以重新检查注入结果，等待时间已足够。
```

## 场景5：异常处理示例

### 步骤1失败
```
❌ 步骤1执行失败
错误原因：测新规则创建失败 - 用例ID不存在或格式错误
建议：请检查caseId格式是否正确，或联系管理员确认用例是否存在
```

### 步骤3循环重试
```
步骤3：查询测新流量列表...
⚠️ 未找到足够的依赖节点（当前最大flowFieldCount: 3）
正在重新触发ETE执行以生成更多流量...

步骤2：重新触发ETE执行...
✓ ETE执行触发成功

步骤3：再次查询测新流量列表...
✓ 找到合适的caseInstanceId: xxx (flowFieldCount: 12)
```

### 步骤5全部失败
```
❌ 步骤5执行失败
错误原因：所有异常注入都失败了
详细信息：
- 总计12个注入用例
- 失败12个 (100%)
- 主要失败原因：目标服务集群不可用

建议：注入失败，需要bklight人员排查服务可用性问题
```

## 场景6：智能意图分析实战

### 上下文感知分析

#### 场景A：用户有等待中的注入
```bash
# 用户状态：步骤1-4已完成，步骤5有等待中的注入
curl -X POST "http://localhost:8080/api/bklight/agent/smart-chat" \
  -d "userId=user001&userInput=检查"
```

**分析过程**:
```
大模型分析提示词：
- 用户输入："检查"
- 当前上下文：Has Waiting Injections: true
- 意图分析：wait_check（因为有等待中的注入）
```

**Agent响应**:
```
正在检查等待中的异常注入结果...
当前注入状态已更新：
- SUCCESS: 8个 (80%)
- FAIL: 1个 (10%)
- WAIT: 1个 (10%)
大部分注入已成功完成！
```

#### 场景B：用户无上下文
```bash
# 用户状态：首次使用，无任何执行记录
curl -X POST "http://localhost:8080/api/bklight/agent/smart-chat" \
  -d "userId=newuser&userInput=检查"
```

**分析过程**:
```
大模型分析提示词：
- 用户输入："检查"
- 当前上下文：Current Step: 0, 无执行记录
- 意图分析：check_status（无等待注入，理解为查看状态）
```

**Agent响应**:
```
您还没有开始任何测试流程。
当前状态：无执行记录
建议：请先提供测试参数开始新的BKlight Mock测试流程。
```

### 多语言混合理解

```bash
# 中英文混合
curl -X POST "http://localhost:8080/api/bklight/agent/smart-chat" \
  -d "userId=user001&userInput=请help我start这个testing"
```

**分析结果**: `help_info` 或 `start_new_flow`（根据上下文决定）

```bash
# 技术术语混合
curl -X POST "http://localhost:8080/api/bklight/agent/smart-chat" \
  -d "userId=user001&userInput=ETE执行失败了，retry一下step 2"
```

**分析结果**: `retry_step:2`

### 复杂场景处理

#### 多重意图识别
```bash
curl -X POST "http://localhost:8080/api/bklight/agent/smart-chat" \
  -d "userId=user001&userInput=先查看一下当前状态，如果步骤3失败了就重试，如果都成功了就检查注入结果"
```

**大模型分析**:
```
用户表达了多个条件意图：
1. 首先查看状态
2. 条件重试步骤3
3. 条件检查注入结果

主要意图：check_status
系统会先显示状态，然后根据实际情况提供后续建议
```

#### 情感化表达
```bash
curl -X POST "http://localhost:8080/api/bklight/agent/smart-chat" \
  -d "userId=user001&userInput=测试跑了好久都没结果，我有点着急，能帮我看看到底怎么回事吗？"
```

**大模型分析**:
```
识别情感：用户焦虑、着急
识别问题：测试时间长、无结果
主要意图：check_status + help_info
系统会提供详细状态说明和安慰性回复
```

### 自然语言交互示例

```bash
# 开始执行 - 多样化表达
"开始BKlight测试"
"执行异常注入测试"
"start testing"
"我想开始测试"
"启动一下"
"开始吧"

# 查看状态 - 多样化表达
"当前进度如何？"
"查看执行状态"
"show me the status"
"现在到哪一步了？"
"进展怎么样？"
"状态"

# 重试操作 - 多样化表达
"重新执行步骤3"
"retry step 2"
"步骤4失败了，重试一下"
"再来一遍第二步"
"重做步骤5"

# 等待检查 - 多样化表达
"等待30分钟后检查结果"
"检查注入是否完成"
"查看最新的注入结果"
"现在可以看结果了吗？"
"注入好了没？"

# 清除重新开始 - 多样化表达
"清除当前进度，重新开始"
"reset and start over"
"重新来过"
"全部重置"
"清空重启"
```

## 场景7：API调用序列

### 完整交互流程
```bash
# 1. 开始执行
curl -X POST "http://localhost:8080/api/bklight/agent/smart-execute" \
  -H "Content-Type: application/json" \
  -d '{"userId":"user001","caseId":"test123","caseName":"测试用例",...}'

# 2. 检查状态
curl -X GET "http://localhost:8080/api/bklight/agent/status?userId=user001"

# 3. 如果有失败，重试特定步骤
curl -X POST "http://localhost:8080/api/bklight/agent/execute-specific-step?userId=user001&stepNumber=3"

# 4. 等待后重新检查
curl -X POST "http://localhost:8080/api/bklight/agent/smart-chat" \
  -d "userId=user001&userInput=检查注入结果"

# 5. 完成后清除上下文
curl -X POST "http://localhost:8080/api/bklight/agent/clear-context?userId=user001"
```

## 最佳实践

1. **使用智能接口**：优先使用 `/smart-execute` 和 `/smart-chat` 接口
2. **保持用户ID一致**：同一个测试流程使用相同的userId
3. **合理等待**：对于异步操作，遵循系统建议的等待时间
4. **错误处理**：根据错误提示进行相应的重试或修复操作
5. **状态检查**：定期使用 `/status` 接口检查执行进度
6. **上下文管理**：测试完成后及时清除上下文，避免干扰后续测试
