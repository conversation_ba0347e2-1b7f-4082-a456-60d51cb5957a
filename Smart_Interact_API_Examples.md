# 智能交互API使用示例

## 🚀 新的统一智能接口

### API端点
```
POST /api/bklight/agent/smart-interact
```

### 核心特性
- ✅ **参数自动检查**: 自动验证必填参数并友好提示
- ✅ **意图智能识别**: 使用大模型分析用户输入意图
- ✅ **步骤顺序验证**: 自动检查步骤依赖关系并纠正
- ✅ **上下文管理**: 智能管理用户会话状态
- ✅ **自然语言交互**: 支持多样化表达方式

## 📝 使用示例

### 示例1：首次使用 - 完整参数

```bash
curl -X POST "http://localhost:8080/api/bklight/agent/smart-interact" \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "user123",
    "userInput": "开始执行BKlight Mock测试",
    "caseId": "a9cd8ba6-f714-41ab-bad0-4022a2f60126",
    "caseName": "[余额宝][RPC]余额宝转出页渲染-新架构&新离线包[过yebtradebff]-黑卡+高客用户-提现页面渲染",
    "downStreamInjectType": "PRC_TIME_OUT_EXCEPTION",
    "relatedMachineGroup": "GROUP_20250630104315"
  }'
```

**系统响应**:
```
✅ 参数检查通过，开始执行BKlight Mock测试流程...

步骤1：正在添加测新规则...
✓ 成功创建测新规则，newPlanRuleId: PLAN1749785601048

步骤2：正在触发ETE执行...
✓ ETE执行触发成功

步骤3：正在查询测新流量列表...
✓ 找到合适的caseInstanceId: d9c205c6-9afe-470e-a831-e672252a59a1

步骤4：正在添加依赖配置规则...
✓ 依赖规则创建成功

步骤5：正在查询异常注入执行结果...
当前注入状态：WAIT状态较多，建议等待10-30分钟后再次查询。
```

### 示例2：首次使用 - 缺少参数

```bash
curl -X POST "http://localhost:8080/api/bklight/agent/smart-interact" \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "user123",
    "userInput": "开始执行测试"
  }'
```

**系统响应**:
```
🔧 **参数检查**

检测到您想要执行BKlight Mock测试，但缺少以下必填参数：

- caseId (用例ID)
- caseName (用例名称)
- downStreamInjectType (异常类型，如：PRC_TIME_OUT_EXCEPTION)
- relatedMachineGroup (机器组，如：GROUP_20250630104315)

请提供完整的参数信息，您可以通过以下方式提供：

**方式1：JSON格式请求**
```json
{
  "userId": "your-user-id",
  "userInput": "开始执行测试",
  "caseId": "your-case-id",
  "caseName": "your-case-name",
  "downStreamInjectType": "PRC_TIME_OUT_EXCEPTION",
  "relatedMachineGroup": "GROUP_20250630104315"
}
```

**方式2：自然语言描述**
例如："开始执行测试，用例ID是xxx，用例名称是xxx，异常类型是PRC_TIME_OUT_EXCEPTION，机器组是GROUP_20250630104315"

💡 **提示**：参数一旦设置会保存在您的会话中，后续操作无需重复提供。
```

### 示例3：步骤顺序自动纠正

```bash
curl -X POST "http://localhost:8080/api/bklight/agent/smart-interact" \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "user123",
    "userInput": "我想执行步骤3查询测新流量列表"
  }'
```

**系统响应**（假设步骤1未完成）:
```
⚠️ **步骤顺序检查**

检测到您想执行步骤3（查询测新流量列表），但前置步骤尚未完成。

**自动处理：**系统将自动从步骤1开始执行，确保流程的正确性。

**执行顺序：**
1. ✅ 首先执行步骤1：创建测新规则
2. ➡️ 然后执行步骤2：触发ETE执行
3. ➡️ 最后执行步骤3：查询测新流量列表

正在开始执行...

步骤1：正在添加测新规则...
[执行详情...]
```

### 示例4：自然语言交互

```bash
curl -X POST "http://localhost:8080/api/bklight/agent/smart-interact" \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "user123",
    "userInput": "查看一下当前的执行状态"
  }'
```

**系统响应**:
```
📊 **当前执行状态**

用户: user123
当前步骤: 4
✅ 步骤1 (创建测新规则): 已完成
✅ 步骤2 (触发ETE执行): 已完成  
✅ 步骤3 (查询测新流量列表): 已完成
✅ 步骤4 (添加依赖配置规则): 已完成
⏳ 步骤5 (查询异常注入结果): 进行中

**建议下一步操作：**
- 说"检查注入结果"来查看最新的异常注入状态
- 如果之前有等待提示，现在可以重新检查结果
```

### 示例5：复杂意图理解

```bash
curl -X POST "http://localhost:8080/api/bklight/agent/smart-interact" \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "user123",
    "userInput": "步骤2好像执行失败了，我想重新试一下，然后继续后面的流程"
  }'
```

**系统响应**:
```
🔄 **智能意图分析**

理解您的需求：重试步骤2并继续后续流程

**执行计划：**
1. 重置步骤2及后续步骤状态
2. 重新执行步骤2：触发ETE执行
3. 继续执行步骤3-5

正在开始重试...

步骤2：正在重新触发ETE执行...
[执行详情...]
```

## 🔧 兼容性接口

### 原有smart-chat接口（保持兼容）

```bash
curl -X POST "http://localhost:8080/api/bklight/agent/smart-chat?userId=user123&userInput=开始执行测试"
```

## 📊 功能对比

| 功能特性 | smart-interact | smart-chat | 说明 |
|---------|---------------|------------|------|
| 参数自动检查 | ✅ | ❌ | 新接口自动验证必填参数 |
| 步骤顺序验证 | ✅ | ❌ | 新接口自动检查步骤依赖 |
| 意图智能识别 | ✅ | ✅ | 都支持大模型意图分析 |
| 上下文管理 | ✅ | ✅ | 都支持会话状态管理 |
| 参数传递方式 | JSON Body | Query Params | 新接口支持结构化参数 |
| 错误提示 | 友好详细 | 基础提示 | 新接口提供更好的用户体验 |

## 🎯 最佳实践

### 1. 首次使用
- 推荐使用`smart-interact`接口
- 在JSON中提供完整的必填参数
- 使用清晰的userInput描述意图

### 2. 后续交互
- 可以只提供userId和userInput
- 系统会自动使用之前保存的参数
- 支持自然语言描述各种操作需求

### 3. 错误处理
- 系统会自动检查参数完整性
- 自动验证步骤执行顺序
- 提供详细的错误信息和解决建议

### 4. 状态管理
- 定期使用"查看状态"检查进度
- 根据系统建议进行后续操作
- 必要时使用"重新开始"清除状态

## 🚀 高级用法

### 批量操作
```bash
# 一次性提供所有参数并开始执行
curl -X POST "http://localhost:8080/api/bklight/agent/smart-interact" \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "user123",
    "userInput": "执行完整的BKlight Mock测试流程，包括所有5个步骤",
    "caseId": "test-case-001",
    "caseName": "完整测试用例",
    "downStreamInjectType": "PRC_TIME_OUT_EXCEPTION",
    "relatedMachineGroup": "GROUP_TEST"
  }'
```

### 条件执行
```bash
# 复杂的条件表达
curl -X POST "http://localhost:8080/api/bklight/agent/smart-interact" \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "user123",
    "userInput": "如果步骤3失败了就重试，如果成功了就继续执行步骤4和5"
  }'
```

这个新的智能交互接口提供了更完整、更智能的用户体验，是推荐使用的主要接口。
