#!/bin/bash

# BKlight Mock Agent SmartChat 测试运行脚本

echo "=========================================="
echo "BKlight Mock Agent SmartChat 测试运行脚本"
echo "=========================================="

# 检查Java环境
echo "检查Java环境..."
java -version
if [ $? -ne 0 ]; then
    echo "❌ Java环境未配置，请先安装Java"
    exit 1
fi

# 检查Maven环境
echo "检查Maven环境..."
mvn -version
if [ $? -ne 0 ]; then
    echo "❌ Maven环境未配置，请先安装Maven"
    exit 1
fi

echo "✓ 环境检查通过"
echo ""

# 进入service模块目录
cd app/service

echo "开始运行SmartChat单元测试..."
echo "=========================================="

# 运行特定的SmartChat测试
echo "1. 运行SmartChat纯单元测试（推荐）..."
mvn test -Dtest=SmartChatPureUnitTest

if [ $? -eq 0 ]; then
    echo "✓ SmartChat纯单元测试通过"
else
    echo "❌ SmartChat纯单元测试失败"
fi

echo ""
echo "2. 运行SmartChat单元测试..."
mvn test -Dtest=SmartChatUnitTest

if [ $? -eq 0 ]; then
    echo "✓ SmartChat单元测试通过"
else
    echo "❌ SmartChat单元测试失败"
fi

echo ""
echo "3. 运行SmartChat集成测试..."
mvn test -Dtest=SmartChatIntegrationTest

if [ $? -eq 0 ]; then
    echo "✓ SmartChat集成测试通过"
else
    echo "❌ SmartChat集成测试失败"
fi

echo ""
echo "4. 运行所有BklightMockAgent测试..."
mvn test -Dtest=BklightMockAgentTest

if [ $? -eq 0 ]; then
    echo "✓ BklightMockAgent测试通过"
else
    echo "❌ BklightMockAgent测试失败"
fi

echo ""
echo "=========================================="
echo "测试运行完成"
echo "=========================================="

# 生成测试报告
echo "生成测试报告..."
mvn surefire-report:report

if [ $? -eq 0 ]; then
    echo "✓ 测试报告生成成功"
    echo "报告位置: target/site/surefire-report.html"
else
    echo "⚠️ 测试报告生成失败"
fi

echo ""
echo "=========================================="
echo "脚本执行完成"
echo "=========================================="
