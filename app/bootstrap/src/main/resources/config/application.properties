# 必填字段，编译打包强制指定此工程类型为 SOFABoot
sofa.version=SOFABoot
# 应用名称
spring.application.name=findataquality
# 服务器端口
server.port=8888
management.server.port=9500
# 服务器zone信息
domain.name=@domainname@
inner.domain=${domain.name}
# 应用日志级别
logging.level.com.alipay.findataquality=INFO
# 日志目录，编译打包校验该字段
logging.file.path=/home/<USER>/logs
logging.root=${logging.file.path}
logging.config=classpath:log4j2-spring.xml
# 近段包解析velocity语法判断版本用
sofa_runtime_spring_major_version=4
# 忽略未解析的占位符
sofa.boot.ignore.unresolvable.placeholders=true
# 网商应用开启 Fast ClassLoader 能力，非网商应用通过 hook.sh 中的 SOFABOOT_JAR_LAUNCHER 开启
use_indexJar_launcher=true
# 开启 MOSN-RUNTIME
sofa.boot.layotto.globalSwitch=true
# ------------ rpc 配置 -------------
# 开启 sofa-rpc 服务引用 vip-url 配置合法性校验能力
sofa.boot.rpc.enableVipUrlCheck=true
# ------------ drm 配置 -------------
# 开启DRM资源类的自动注册能力
sofa.boot.ddcs.enableAutoRegister=true
# 开启json序列化，在webGw中使用
com.alipay.sofa.rpc.codec.json.enable=true
# 接入Qwen
#sofa.ai.max.chat.options.scene-name=Qwen2_72B_Instruct_vllm
#sofa.ai.max.chat.options.chain-name=v1
#sofa.ai.max.chat.options.model-type=OPENAI
#sofa.ai.max.chat.options.app-token=f3574380-739c-4e38-848d-f6fae16a8d7d

sofa.ai.antllm.chat.app-token=8lSY89RmkqI6bEyaAWZpZeKxiw3fNz7a
sofa.ai.antllm.chat.options.model=qwen3-235b-a22b
sofa.ai.antllm.embedding.app-token=8lSY89RmkqI6bEyaAWZpZeKxiw3fNz7a
sofa.ai.antllm.embedding.options.model=bge_large

# ------------ MCP Server 配置 -------------
# mcp server 配置, 需要本地启动 mosn 参考：https://yuque.antfin.com/sofa-open/cnar/quickstart-install-layotto#PfdRO
sofa.ai.mcp.server.enabled=true
sofa.ai.mcp.server.name=findataqualityMcpServer
sofa.ai.mcp.server.version=1.0.0

# ------------ MCP Client 配置 -------------
 mcp client 配置, 需要本地启动 mosn 参考：https://yuque.antfin.com/sofa-open/cnar/quickstart-install-layotto#PfdRO
sofa.ai.mcp.client.enabled=true
sofa.ai.mcp.client.name=findataqualityMcpClient
sofa.ai.mcp.client.version=1.0.0
sofa.ai.mcp.client.request-timeout=30s
## 通过 mosn 寻址 server-code
##sofa.ai.mcp.client.sse.connections.sofadocmcpserver.server-code=mcp.ant.apicapture.apicapture
## 应用自己发布MCP服务同时自己引用，通过直连方式寻址
sofa.ai.mcp.client.sse.connections.sofadocmcpserver.url=http://localhost:11768
sofa.ai.mcp.client.sse.connections.sofadocmcpserver.sse-endpoint=/sse
## 用于应用自己发布MCP服务同时自己引用，mcp client 延迟实例化，默认false
sofa.ai.mcp.client.sse.connections.sofadocmcpserver.lazy=true
## mcp client toolcallback 配置，将 MCP 服务自动注册为 ToolCallbackProvider Bean
sofa.ai.mcp.client.toolcallback.enabled=true

# ==================== 大模型配置 ====================

# 大模型名称配置
# 默认值: Qwen3-235B-A22B
# 可选值: Qwen3-235B-A22B, GPT-4, Claude-3, 等
bklight.agent.llm.model=Qwen3-235B-A22B

# 大模型温度配置（用于一般对话）
# 范围: 0.0 - 2.0
# 0.0: 最确定性的输出
# 1.0: 平衡创造性和确定性
# 2.0: 最具创造性的输出
# 默认值: 0.1
bklight.agent.llm.temperature=0.1

# 意图分析专用温度配置
# 用于用户意图分析，建议使用较低的温度确保结果稳定
# 默认值: 0.1
bklight.agent.llm.intent-analysis.temperature=0.1

# ==================== 其他可选配置 ====================

# 最大重试次数（当大模型调用失败时）
# 默认值: 3
bklight.agent.llm.max-retry=3

# 请求超时时间（秒）
# 默认值: 30
bklight.agent.llm.timeout=30

# 是否启用意图分析缓存
# 默认值: true
bklight.agent.intent-analysis.cache.enabled=true

# 意图分析缓存过期时间（分钟）
# 默认值: 60
bklight.agent.intent-analysis.cache.expire-minutes=60

# ==================== 环境特定配置 ====================

# 开发环境配置
# 开发环境可以使用更快的模型或更高的温度进行测试
#bklight.agent.llm.model=Qwen3-72B-A14B
#bklight.agent.llm.temperature=0.2

# 生产环境配置
# 生产环境建议使用稳定的模型和较低的温度
#bklight.agent.llm.model=Qwen3-235B-A22B
#bklight.agent.llm.temperature=0.1

# 测试环境配置
# 测试环境可以使用模拟的模型响应
#bklight.agent.llm.model=MockModel
#bklight.agent.llm.temperature=0.0

# ==================== 使用说明 ====================

# 1. 模型选择建议：
#    - Qwen3-235B-A22B: 推荐用于生产环境，准确性高
#    - Qwen3-72B-A14B: 适合开发环境，响应速度快
#    - GPT-4: 如果有OpenAI API访问权限
#    - Claude-3: 如果有Anthropic API访问权限

# 2. 温度设置建议：
#    - 意图分析: 0.0-0.2 (需要准确性)
#    - 一般对话: 0.1-0.5 (平衡准确性和自然度)
#    - 创意生成: 0.5-1.0 (需要创造性)

# 3. 配置优先级：
#    - 环境变量 > application-{profile}.properties > application.properties
#    - 可以通过环境变量覆盖配置，例如：
#      BKLIGHT_AGENT_LLM_MODEL=GPT-4

# 4. 动态配置：
#    - 支持Spring Boot的配置热更新
#    - 可以通过管理端点动态修改配置

# ==================== 监控和日志 ====================

# 启用大模型调用监控
bklight.agent.monitoring.enabled=true

# 记录大模型调用日志
bklight.agent.logging.llm-calls=true

# 记录意图分析详情
bklight.agent.logging.intent-analysis=true

# 性能指标收集
bklight.agent.metrics.enabled=true

# ==================== 示例配置组合 ====================

# 高性能配置（适合高并发场景）
#bklight.agent.llm.model=Qwen3-72B-A14B
#bklight.agent.llm.temperature=0.1
#bklight.agent.llm.timeout=15
#bklight.agent.llm.max-retry=2

# 高准确性配置（适合关键业务场景）
#bklight.agent.llm.model=Qwen3-235B-A22B
#bklight.agent.llm.temperature=0.05
#bklight.agent.intent-analysis.temperature=0.0
#bklight.agent.llm.timeout=60
#bklight.agent.llm.max-retry=5

# 开发调试配置
#bklight.agent.llm.model=Qwen3-72B-A14B
#bklight.agent.llm.temperature=0.3
#bklight.agent.logging.llm-calls=true
#bklight.agent.logging.intent-analysis=true
