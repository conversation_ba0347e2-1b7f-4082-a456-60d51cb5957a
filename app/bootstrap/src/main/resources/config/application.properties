# 必填字段，编译打包强制指定此工程类型为 SOFABoot
sofa.version=SOFABoot
# 应用名称
spring.application.name=findataquality
# 服务器端口
server.port=8888
management.server.port=9500
# 服务器zone信息
domain.name=@domainname@
inner.domain=${domain.name}
# 应用日志级别
logging.level.com.alipay.findataquality=INFO
# 日志目录，编译打包校验该字段
logging.file.path=/home/<USER>/logs
logging.root=${logging.file.path}
logging.config=classpath:log4j2-spring.xml
# 近段包解析velocity语法判断版本用
sofa_runtime_spring_major_version=4
# 忽略未解析的占位符
sofa.boot.ignore.unresolvable.placeholders=true
# 网商应用开启 Fast ClassLoader 能力，非网商应用通过 hook.sh 中的 SOFABOOT_JAR_LAUNCHER 开启
use_indexJar_launcher=true
# 开启 MOSN-RUNTIME
sofa.boot.layotto.globalSwitch=true
# ------------ rpc 配置 -------------
# 开启 sofa-rpc 服务引用 vip-url 配置合法性校验能力
sofa.boot.rpc.enableVipUrlCheck=true
# ------------ drm 配置 -------------
# 开启DRM资源类的自动注册能力
sofa.boot.ddcs.enableAutoRegister=true
# 开启json序列化，在webGw中使用
com.alipay.sofa.rpc.codec.json.enable=true

#sofa.ai.max.chat.options.chain-name=v1
#sofa.ai.max.chat.options.model-type=OPENAI
#sofa.ai.max.chat.options.app-token=f3574380-739c-4e38-848d-f6fae16a8d7d

sofa.ai.antllm.chat.app-token=8lSY89RmkqI6bEyaAWZpZeKxiw3fNz7a
sofa.ai.antllm.chat.options.model=qwen3-235b-a22b
sofa.ai.antllm.embedding.app-token=8lSY89RmkqI6bEyaAWZpZeKxiw3fNz7a
sofa.ai.antllm.embedding.options.model=bge_large
# ------------ MCP Server 配置 -------------
# mcp server 配置, 需要本地启动 mosn 参考：https://yuque.antfin.com/sofa-open/cnar/quickstart-install-layotto#PfdRO
sofa.ai.mcp.server.enabled=true
sofa.ai.mcp.server.name=findataquality-mcp-server
sofa.ai.mcp.server.version=1.0.0

# ------------ MCP Client 配置 -------------
# mcp client 配置, 需要本地启动 mosn 参考：https://yuque.antfin.com/sofa-open/cnar/quickstart-install-layotto#PfdRO
sofa.ai.mcp.client.enabled=true
sofa.ai.mcp.client.name=findataquality-mcp-client
sofa.ai.mcp.client.version=1.0.0
sofa.ai.mcp.client.request-timeout=30s
## 通过 mosn 寻址 server-code
##sofa.ai.mcp.client.sse.connections.sofadocmcpserver.server-code=mcp.ant.apicapture.apicapture
## 应用自己发布MCP服务同时自己引用，通过直连方式寻址
sofa.ai.mcp.client.sse.connections.sofadocmcpserver.url=http://localhost:11768
sofa.ai.mcp.client.sse.connections.sofadocmcpserver.sse-endpoint=/sse
## 用于应用自己发布MCP服务同时自己引用，mcp client 延迟实例化，默认false
sofa.ai.mcp.client.sse.connections.sofadocmcpserver.lazy=true
## mcp client toolcallback 配置，将 MCP 服务自动注册为 ToolCallbackProvider Bean
sofa.ai.mcp.client.toolcallback.enabled=true

