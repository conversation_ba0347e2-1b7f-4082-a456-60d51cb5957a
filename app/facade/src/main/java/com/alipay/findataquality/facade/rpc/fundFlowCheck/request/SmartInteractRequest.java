package com.alipay.findataquality.facade.rpc.fundFlowCheck.request;

public class SmartInteractRequest {

    private String userId;
    private String userInput;
    private String caseId;
    private String caseName;
    private String downStreamInjectType;
    private String relatedMachineGroup;

    // Getters and Setters
    public String getUserId() { return userId; }
    public void setUserId(String userId) { this.userId = userId; }
    public String getUserInput() { return userInput; }
    public void setUserInput(String userInput) { this.userInput = userInput; }
    public String getCaseId() { return caseId; }
    public void setCaseId(String caseId) { this.caseId = caseId; }
    public String getCaseName() { return caseName; }
    public void setCaseName(String caseName) { this.caseName = caseName; }
    public String getDownStreamInjectType() { return downStreamInjectType; }
    public void setDownStreamInjectType(String downStreamInjectType) { this.downStreamInjectType = downStreamInjectType; }
    public String getRelatedMachineGroup() { return relatedMachineGroup; }
    public void setRelatedMachineGroup(String relatedMachineGroup) { this.relatedMachineGroup = relatedMachineGroup; }


}
