package com.alipay.findataquality.facade.rpc.dependencyAnalyzer.result;

import com.alibaba.fastjson.JSONArray;

public class NewTestQueryInjectsResult {

    JSONArray successDataList;
    JSONArray waitDataList;
    JSONArray failDataList;
    String message;
    boolean queryInjectResultListStatus;
    int successCount;
    int waitCount;
    int failCount;


    public JSONArray getSuccessDataList() {
        return successDataList;
    }

    public void setSuccessDataList(JSONArray successDataList) {
        this.successDataList = successDataList;
    }

    public JSONArray getWaitDataList() {
        return waitDataList;
    }

    public void setWaitDataList(JSONArray waitDataList) {
        this.waitDataList = waitDataList;
    }

    public JSONArray getFailDataList() {
        return failDataList;
    }

    public void setFailDataList(JSONArray failDataList) {
        this.failDataList = failDataList;
    }

    public boolean isQueryInjectResultListStatus() {
        return queryInjectResultListStatus;
    }

    public void setQueryInjectResultListStatus(boolean queryInjectResultListStatus) {
        this.queryInjectResultListStatus = queryInjectResultListStatus;
    }

    public int getSuccessCount() {
        return successCount;
    }

    public void setSuccessCount(int successCount) {
        this.successCount = successCount;
    }

    public int getWaitCount() {
        return waitCount;
    }

    public void setWaitCount(int waitCount) {
        this.waitCount = waitCount;
    }

    public int getFailCount() {
        return failCount;
    }

    public void setFailCount(int failCount) {
        this.failCount = failCount;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
