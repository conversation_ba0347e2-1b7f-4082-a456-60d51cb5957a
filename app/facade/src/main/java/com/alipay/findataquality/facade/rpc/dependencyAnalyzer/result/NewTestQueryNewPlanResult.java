package com.alipay.findataquality.facade.rpc.dependencyAnalyzer.result;

public class NewTestQueryNewPlanResult {

    boolean queryNewPlanListStatus;
    String caseInstanceId;
    String message;

    public boolean isQueryNewPlanListStatus() {
        return queryNewPlanListStatus;
    }

    public void setQueryNewPlanListStatus(boolean queryNewPlanListStatus) {
        this.queryNewPlanListStatus = queryNewPlanListStatus;
    }

    public String getCaseInstanceId() {
        return caseInstanceId;
    }

    public void setCaseInstanceId(String caseInstanceId) {
        this.caseInstanceId = caseInstanceId;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
