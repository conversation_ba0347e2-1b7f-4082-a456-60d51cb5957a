package com.alipay.findataquality.facade.rpc.dependencyAnalyzer.result;

public class NewTestTriggerEteResult {

    boolean eteExecuteSuccess;
    String traceId;
    String message;

    public boolean isEteExecuteSuccess() {
        return eteExecuteSuccess;
    }

    public void setEteExecuteSuccess(boolean eteExecuteSuccess) {
        this.eteExecuteSuccess = eteExecuteSuccess;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
