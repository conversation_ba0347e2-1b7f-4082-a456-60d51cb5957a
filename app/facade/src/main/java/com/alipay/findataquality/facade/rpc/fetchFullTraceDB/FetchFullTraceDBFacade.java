package com.alipay.findataquality.facade.rpc.fetchFullTraceDB;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.model.QuerySceneResult;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.model.QueryTaskResult;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.request.FetchDataRequest;

/**
 * @ClassName FetchFullTraceDBFacade
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/22 16:20
 * @Version V1.0
 **/
public interface FetchFullTraceDBFacade {
    /**
     * 根据指定场景全链路取数
     * @return
     */
    public QueryTaskResult fetchDataScene(FetchDataRequest request);

    /**
     * 查询所有可用的取数场景
     * @return
     */
    public QuerySceneResult getAllScenes();
}
