package com.alipay.findataquality.service.ai.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * BKlight Agent 配置管理控制器
 * 提供配置查看和动态修改功能
 */
@RestController
@RequestMapping("/api/bklight/agent/config")
public class BklightAgentConfigController {

    @Autowired
    private BklightAgentConfig agentConfig;

    /**
     * 获取当前配置
     */
    @GetMapping("/current")
    public Map<String, Object> getCurrentConfig() {
        Map<String, Object> config = new HashMap<>();
        
        // LLM配置
        Map<String, Object> llmConfig = new HashMap<>();
        llmConfig.put("model", agentConfig.getLlm().getModel());
        llmConfig.put("temperature", agentConfig.getLlm().getTemperature());
        llmConfig.put("maxRetry", agentConfig.getLlm().getMaxRetry());
        llmConfig.put("timeout", agentConfig.getLlm().getTimeout());
        config.put("llm", llmConfig);
        
        // 意图分析配置
        Map<String, Object> intentConfig = new HashMap<>();
        intentConfig.put("temperature", agentConfig.getIntentAnalysis().getTemperature());
        
        Map<String, Object> cacheConfig = new HashMap<>();
        cacheConfig.put("enabled", agentConfig.getIntentAnalysis().getCache().getEnabled());
        cacheConfig.put("expireMinutes", agentConfig.getIntentAnalysis().getCache().getExpireMinutes());
        intentConfig.put("cache", cacheConfig);
        config.put("intentAnalysis", intentConfig);
        
        // 监控配置
        config.put("monitoring", Map.of("enabled", agentConfig.getMonitoring().getEnabled()));
        
        // 日志配置
        Map<String, Object> loggingConfig = new HashMap<>();
        loggingConfig.put("llmCalls", agentConfig.getLogging().getLlmCalls());
        loggingConfig.put("intentAnalysis", agentConfig.getLogging().getIntentAnalysis());
        config.put("logging", loggingConfig);
        
        // 指标配置
        config.put("metrics", Map.of("enabled", agentConfig.getMetrics().getEnabled()));
        
        return config;
    }

    /**
     * 获取配置摘要
     */
    @GetMapping("/summary")
    public String getConfigSummary() {
        return agentConfig.getConfigSummary();
    }

    /**
     * 验证配置有效性
     */
    @GetMapping("/validate")
    public Map<String, Object> validateConfig() {
        Map<String, Object> result = new HashMap<>();
        result.put("valid", agentConfig.isValid());
        
        if (!agentConfig.isValid()) {
            result.put("errors", getValidationErrors());
        }
        
        return result;
    }

    /**
     * 更新LLM模型
     */
    @PostMapping("/llm/model")
    public Map<String, Object> updateLlmModel(@RequestParam String model) {
        String oldModel = agentConfig.getLlm().getModel();
        agentConfig.getLlm().setModel(model);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("oldModel", oldModel);
        result.put("newModel", model);
        result.put("message", "LLM model updated successfully");
        
        return result;
    }

    /**
     * 更新LLM温度
     */
    @PostMapping("/llm/temperature")
    public Map<String, Object> updateLlmTemperature(@RequestParam Float temperature) {
        if (temperature < 0.0f || temperature > 2.0f) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "Temperature must be between 0.0 and 2.0");
            return error;
        }
        
        Float oldTemperature = agentConfig.getLlm().getTemperature();
        agentConfig.getLlm().setTemperature(temperature);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("oldTemperature", oldTemperature);
        result.put("newTemperature", temperature);
        result.put("message", "LLM temperature updated successfully");
        
        return result;
    }

    /**
     * 更新意图分析温度
     */
    @PostMapping("/intent-analysis/temperature")
    public Map<String, Object> updateIntentAnalysisTemperature(@RequestParam Float temperature) {
        if (temperature < 0.0f || temperature > 2.0f) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "Temperature must be between 0.0 and 2.0");
            return error;
        }
        
        Float oldTemperature = agentConfig.getIntentAnalysis().getTemperature();
        agentConfig.getIntentAnalysis().setTemperature(temperature);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("oldTemperature", oldTemperature);
        result.put("newTemperature", temperature);
        result.put("message", "Intent analysis temperature updated successfully");
        
        return result;
    }

    /**
     * 切换监控状态
     */
    @PostMapping("/monitoring/toggle")
    public Map<String, Object> toggleMonitoring() {
        Boolean oldStatus = agentConfig.getMonitoring().getEnabled();
        agentConfig.getMonitoring().setEnabled(!oldStatus);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("oldStatus", oldStatus);
        result.put("newStatus", !oldStatus);
        result.put("message", "Monitoring status toggled successfully");
        
        return result;
    }

    /**
     * 切换日志记录
     */
    @PostMapping("/logging/llm-calls/toggle")
    public Map<String, Object> toggleLlmCallsLogging() {
        Boolean oldStatus = agentConfig.getLogging().getLlmCalls();
        agentConfig.getLogging().setLlmCalls(!oldStatus);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("oldStatus", oldStatus);
        result.put("newStatus", !oldStatus);
        result.put("message", "LLM calls logging toggled successfully");
        
        return result;
    }

    /**
     * 重置为生产环境配置
     */
    @PostMapping("/reset/production")
    public Map<String, Object> resetToProductionConfig() {
        BklightAgentConfig prodConfig = BklightAgentConfig.getProductionConfig();
        
        // 复制生产环境配置
        agentConfig.getLlm().setModel(prodConfig.getLlm().getModel());
        agentConfig.getLlm().setTemperature(prodConfig.getLlm().getTemperature());
        agentConfig.getIntentAnalysis().setTemperature(prodConfig.getIntentAnalysis().getTemperature());
        agentConfig.getLlm().setTimeout(prodConfig.getLlm().getTimeout());
        agentConfig.getLlm().setMaxRetry(prodConfig.getLlm().getMaxRetry());
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "Configuration reset to production defaults");
        result.put("config", getCurrentConfig());
        
        return result;
    }

    /**
     * 重置为开发环境配置
     */
    @PostMapping("/reset/development")
    public Map<String, Object> resetToDevelopmentConfig() {
        BklightAgentConfig devConfig = BklightAgentConfig.getDevelopmentConfig();
        
        // 复制开发环境配置
        agentConfig.getLlm().setModel(devConfig.getLlm().getModel());
        agentConfig.getLlm().setTemperature(devConfig.getLlm().getTemperature());
        agentConfig.getIntentAnalysis().setTemperature(devConfig.getIntentAnalysis().getTemperature());
        agentConfig.getLlm().setTimeout(devConfig.getLlm().getTimeout());
        agentConfig.getLlm().setMaxRetry(devConfig.getLlm().getMaxRetry());
        agentConfig.getLogging().setLlmCalls(devConfig.getLogging().getLlmCalls());
        agentConfig.getLogging().setIntentAnalysis(devConfig.getLogging().getIntentAnalysis());
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "Configuration reset to development defaults");
        result.put("config", getCurrentConfig());
        
        return result;
    }

    /**
     * 获取配置验证错误
     */
    private Map<String, String> getValidationErrors() {
        Map<String, String> errors = new HashMap<>();
        
        if (agentConfig.getLlm().getModel() == null || agentConfig.getLlm().getModel().trim().isEmpty()) {
            errors.put("llm.model", "Model name cannot be empty");
        }
        
        if (agentConfig.getLlm().getTemperature() == null || 
            agentConfig.getLlm().getTemperature() < 0.0f || 
            agentConfig.getLlm().getTemperature() > 2.0f) {
            errors.put("llm.temperature", "Temperature must be between 0.0 and 2.0");
        }
        
        if (agentConfig.getIntentAnalysis().getTemperature() == null || 
            agentConfig.getIntentAnalysis().getTemperature() < 0.0f || 
            agentConfig.getIntentAnalysis().getTemperature() > 2.0f) {
            errors.put("intentAnalysis.temperature", "Temperature must be between 0.0 and 2.0");
        }
        
        return errors;
    }
}
