package com.alipay.findataquality.service.ai.mcp;

import com.alipay.sofa.ai.antllm.AntLLMChatModel;
import com.alipay.sofa.ai.antllm.AntLLMChatOptions;
import com.alipay.sofa.ai.mcp.SofaMcpToolCallbackProvider;
import com.alipay.sofa.ai.mcp.client.SofaMcpClientFactory;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/mcp/client")
public class McpClientController {

    @Autowired(required = false)
    private SofaMcpClientFactory sofaMcpClientFactory;

    @Autowired(required = false)
    private AntLLMChatModel antLLMChatModel;

    @GetMapping("/tools")
    public List<ToolCallback> tools() {
        SofaMcpToolCallbackProvider sofaMcpToolCallbackProvider =
                new SofaMcpToolCallbackProvider(sofaMcpClientFactory.listMcpClients());
        ToolCallback[] toolCallbacks = sofaMcpToolCallbackProvider.getToolCallbacks();
        if ( toolCallbacks != null ) {
            return Arrays.asList(toolCallbacks);
        } else {
            return Arrays.asList();
        }
    }

    @GetMapping("/chat")
    public String chatWithMcp(@RequestParam String input, HttpServletResponse httpServletResponse) {
        httpServletResponse.setCharacterEncoding("UTF-8");
        httpServletResponse.setContentType("text/event-stream");
        SofaMcpToolCallbackProvider sofaMcpToolCallbackProvider =
                new SofaMcpToolCallbackProvider(sofaMcpClientFactory.listMcpClients());
        Prompt prompt = new Prompt(input, AntLLMChatOptions.builder()
                .withModel("Qwen3-235B-A22B")
                .withToolCallbacks(sofaMcpToolCallbackProvider.getToolCallbacks()).build());
        ChatResponse response = antLLMChatModel.call(prompt);
        return response.getResult().getOutput().getText();
    }

    private String generateResponse(ChatResponse chatResponse) {
        return chatResponse.getResult().getOutput().toString();
    }


}
