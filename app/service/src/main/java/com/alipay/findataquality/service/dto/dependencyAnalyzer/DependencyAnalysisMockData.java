package com.alipay.findataquality.service.dto.dependencyAnalyzer;

/**
 * 依赖分析Mock数据模型
 */
public class DependencyAnalysisMockData {
    /**
     * 规则ID
     */
    private String ruleId;
    
    /**
     * Case类型
     */
    private String caseType;
    
    /**
     * Case ID
     */
    private String caseId;
    
    /**
     * 基准实例ID
     */
    private String standardInstanceId;
    
    /**
     * 上游依赖节点ID
     */
    private String upperNodeId;
    
    /**
     * 上游依赖节点所属应用
     */
    private String upperNodeApp;
    
    /**
     * 上游依赖节点名称
     */
    private String upperNodeName;
    
    /**
     * 下游异常注入类型
     */
    private String downNodeInjectType;
    
    /**
     * 联调分组
     */
    private String groupId;
    
    /**
     * 上游依赖节点监控规则
     */
    private String upperNodeMonitorRule;
    
    /**
     * 下游节点ID
     */
    private String downNodeId;
    
    /**
     * 下游节点名称
     */
    private String downNodeServiceName;
    
    /**
     * 下游节点应用名称
     */
    private String downNodeAppName;
    
    /**
     * 下游异常注入规则
     */
    private String downInjectRule;
    
    /**
     * 下游异常注入类型v2
     */
    private String downInjectTypeV2;
    
    /**
     * 下游异常规则状态
     */
    private String downErrorRuleStatus;
    
    /**
     * 下游规则命中实例ID
     */
    private String downRuleHitInstanceId;
    
    /**
     * 依赖节点初始入参
     */
    private String dependencyNodeInitParam;
    
    /**
     * 依赖节点规则注入入参
     */
    private String dependencyNodeRuleInjectParam;
    
    /**
     * 依赖节点初始返回值
     */
    private String dependencyNodeInitResponse;
    
    /**
     * 依赖节点规则注入返回值
     */
    private String dependencyNodeRuleInjectResponse;
    
    /**
     * 依赖节点初始异常信息
     */
    private String dependencyNodeInitErrorInfo;
    
    /**
     * 依赖节点规则注入异常信息
     */
    private String dependencyNodeRuleInjectErrorInfo;
    
    /**
     * 实例执行上下文信息
     */
    private String instanceExecContext;
    
    /**
     * 命中实例节点返回值
     */
    private String hitInstanceNodeResponse;

    // Getter and Setter methods
    public String getRuleId() {
        return ruleId;
    }

    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }

    public String getCaseType() {
        return caseType;
    }

    public void setCaseType(String caseType) {
        this.caseType = caseType;
    }

    public String getCaseId() {
        return caseId;
    }

    public void setCaseId(String caseId) {
        this.caseId = caseId;
    }

    public String getStandardInstanceId() {
        return standardInstanceId;
    }

    public void setStandardInstanceId(String standardInstanceId) {
        this.standardInstanceId = standardInstanceId;
    }

    public String getUpperNodeId() {
        return upperNodeId;
    }

    public void setUpperNodeId(String upperNodeId) {
        this.upperNodeId = upperNodeId;
    }

    public String getUpperNodeApp() {
        return upperNodeApp;
    }

    public void setUpperNodeApp(String upperNodeApp) {
        this.upperNodeApp = upperNodeApp;
    }

    public String getUpperNodeName() {
        return upperNodeName;
    }

    public void setUpperNodeName(String upperNodeName) {
        this.upperNodeName = upperNodeName;
    }

    public String getDownNodeInjectType() {
        return downNodeInjectType;
    }

    public void setDownNodeInjectType(String downNodeInjectType) {
        this.downNodeInjectType = downNodeInjectType;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getUpperNodeMonitorRule() {
        return upperNodeMonitorRule;
    }

    public void setUpperNodeMonitorRule(String upperNodeMonitorRule) {
        this.upperNodeMonitorRule = upperNodeMonitorRule;
    }

    public String getDownNodeId() {
        return downNodeId;
    }

    public void setDownNodeId(String downNodeId) {
        this.downNodeId = downNodeId;
    }

    public String getDownNodeServiceName() {
        return downNodeServiceName;
    }

    public void setDownNodeServiceName(String downNodeServiceName) {
        this.downNodeServiceName = downNodeServiceName;
    }

    public String getDownNodeAppName() {
        return downNodeAppName;
    }

    public void setDownNodeAppName(String downNodeAppName) {
        this.downNodeAppName = downNodeAppName;
    }

    public String getDownInjectRule() {
        return downInjectRule;
    }

    public void setDownInjectRule(String downInjectRule) {
        this.downInjectRule = downInjectRule;
    }

    public String getDownInjectTypeV2() {
        return downInjectTypeV2;
    }

    public void setDownInjectTypeV2(String downInjectTypeV2) {
        this.downInjectTypeV2 = downInjectTypeV2;
    }

    public String getDownErrorRuleStatus() {
        return downErrorRuleStatus;
    }

    public void setDownErrorRuleStatus(String downErrorRuleStatus) {
        this.downErrorRuleStatus = downErrorRuleStatus;
    }

    public String getDownRuleHitInstanceId() {
        return downRuleHitInstanceId;
    }

    public void setDownRuleHitInstanceId(String downRuleHitInstanceId) {
        this.downRuleHitInstanceId = downRuleHitInstanceId;
    }

    public String getDependencyNodeInitParam() {
        return dependencyNodeInitParam;
    }

    public void setDependencyNodeInitParam(String dependencyNodeInitParam) {
        this.dependencyNodeInitParam = dependencyNodeInitParam;
    }

    public String getDependencyNodeRuleInjectParam() {
        return dependencyNodeRuleInjectParam;
    }

    public void setDependencyNodeRuleInjectParam(String dependencyNodeRuleInjectParam) {
        this.dependencyNodeRuleInjectParam = dependencyNodeRuleInjectParam;
    }

    public String getDependencyNodeInitResponse() {
        return dependencyNodeInitResponse;
    }

    public void setDependencyNodeInitResponse(String dependencyNodeInitResponse) {
        this.dependencyNodeInitResponse = dependencyNodeInitResponse;
    }

    public String getDependencyNodeRuleInjectResponse() {
        return dependencyNodeRuleInjectResponse;
    }

    public void setDependencyNodeRuleInjectResponse(String dependencyNodeRuleInjectResponse) {
        this.dependencyNodeRuleInjectResponse = dependencyNodeRuleInjectResponse;
    }

    public String getDependencyNodeInitErrorInfo() {
        return dependencyNodeInitErrorInfo;
    }

    public void setDependencyNodeInitErrorInfo(String dependencyNodeInitErrorInfo) {
        this.dependencyNodeInitErrorInfo = dependencyNodeInitErrorInfo;
    }

    public String getDependencyNodeRuleInjectErrorInfo() {
        return dependencyNodeRuleInjectErrorInfo;
    }

    public void setDependencyNodeRuleInjectErrorInfo(String dependencyNodeRuleInjectErrorInfo) {
        this.dependencyNodeRuleInjectErrorInfo = dependencyNodeRuleInjectErrorInfo;
    }

    public String getInstanceExecContext() {
        return instanceExecContext;
    }

    public void setInstanceExecContext(String instanceExecContext) {
        this.instanceExecContext = instanceExecContext;
    }

    public String getHitInstanceNodeResponse() {
        return hitInstanceNodeResponse;
    }

    public void setHitInstanceNodeResponse(String hitInstanceNodeResponse) {
        this.hitInstanceNodeResponse = hitInstanceNodeResponse;
    }
}
