package com.alipay.findataquality.service.ai.mcp;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjsonfordrm.JSON;
import com.alipay.findataquality.facade.rpc.dependencyAnalyzer.model.BklightMockData;
import com.alipay.findataquality.facade.rpc.dependencyAnalyzer.model.DependencyAnalyzerModel;
import com.alipay.findataquality.facade.rpc.dependencyAnalyzer.result.*;
import com.alipay.findataquality.service.dto.dependencyAnalyzer.EteExecResultDTO;
import com.alipay.findataquality.service.dto.dependencyAnalyzer.UIMockQueryDetailResult;
import com.alipay.findataquality.service.util.BakeryToolsUtil;
import com.alipay.findataquality.service.util.EteUtil;
import com.alipay.findataquality.service.util.HttpUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.tool.annotation.Tool;

import java.util.*;

import static com.alipay.findataquality.service.constant.DependencyAnalyzerConstant.DEPENDENCY_JUDGE_TYPE_STRONG;

public class MCPServerBKlightMockTools {

    private static final Logger logger = LoggerFactory.getLogger(MCPServerBKlightMockTools.class);

    @Tool(name="addNewPlan", description = "Add a new test plan rule. Input: caseId (test case ID) and caseName (test case name). Output: newly created test plan rule ID (newPlanRuleId)")
    public NewTestAddNewPlanResult toolsForAddNewPlan(String caseId, String caseName) {
        NewTestAddNewPlanResult newTestAddNewPlanResult = new NewTestAddNewPlanResult();

        //新增测新规则
        String ADD_NEW_PLAN_RULE_URL = "http://bklight.alipay.com/api/open/newPlan/addRule.json";
        Map<String, String> addNewPlanRuleRequest = new HashMap<>();
        addNewPlanRuleRequest.put("caseType", "E2E");
        addNewPlanRuleRequest.put("caseId", caseId);
        addNewPlanRuleRequest.put("caseName", caseName);
        addNewPlanRuleRequest.put("env", "DEV");

        String newPlanAddRuleResp = HttpUtil.sendHttpPostCommon(ADD_NEW_PLAN_RULE_URL, JSONObject.toJSONString(addNewPlanRuleRequest));

        JSONObject newPlanAddRuleRespJsonObj = JSONObject.parseObject(newPlanAddRuleResp);
        String newPlanRuleId = newPlanAddRuleRespJsonObj.getJSONObject("data").getString("ruleId");
        newTestAddNewPlanResult.setNewPlanRuleId(newPlanRuleId);
        boolean addNewPlanRuleStatus = newPlanAddRuleRespJsonObj.getJSONObject("data").getBoolean("success");
        newTestAddNewPlanResult.setAddNewPlanRuleStatus(addNewPlanRuleStatus);
        String message  = newPlanAddRuleRespJsonObj.getJSONObject("data").getString("message");
        newTestAddNewPlanResult.setMessage(message);

        logger.info("adding new Plan Rule status: {},new plan rule id: {}" + addNewPlanRuleStatus,newPlanRuleId);
        return newTestAddNewPlanResult;
    }

    @Tool(name="triggerEte", description = "Trigger ETE (End-to-End Test Engine) execution. Input: caseId (test case ID). Output: whether ETE execution was triggered successfully")
    public NewTestTriggerEteResult toolsForTriggerEte(String caseId) {
        NewTestTriggerEteResult newTestTriggerEteResult = new NewTestTriggerEteResult();
        //执行ETE结果
        String ETE_EXECUTE_RET_API = "http://*************/model/caseFlowModelRun.json";

        Map<String, Object> requestParams = new HashMap<>();
        Map<String, Object> environmentVariables = new HashMap<>();
        environmentVariables.put("env", "dev");
        requestParams.put("environmentVariables", environmentVariables);

        Map<String, String> context = new HashMap<>();
        context.put("flowInstanceCode", caseId);
        requestParams.put("context", context);
        String req = JSON.toJSONString(requestParams);
        EteExecResultDTO triggerETEResp = EteUtil.executeEteTest(ETE_EXECUTE_RET_API, req);

        logger.info("trigger ETE resp:{} ",JSON.toJSONString(triggerETEResp));
        newTestTriggerEteResult.setEteExecuteSuccess(triggerETEResp.getSuccess());
        newTestTriggerEteResult.setTraceId(triggerETEResp.getTraceId());
        newTestTriggerEteResult.setMessage(triggerETEResp.getMessage());
        return newTestTriggerEteResult;
    }

    @Tool(name="queryNewPlanList", description = "Query new plan traffic list. Input: caseId (test case ID) and newPlanRuleId (new plan rule ID). Output: caseInstanceId with the most dependency nodes")
    public NewTestQueryNewPlanResult toolsForQueryNewPlanList(String caseId, String newPlanRuleId) {
        NewTestQueryNewPlanResult result = new NewTestQueryNewPlanResult();

        //查询测新规则列表-获取绘制了链路依赖的规则
        String QUERY_NEW_PLAN_RULE_URL = "http://bklight.alipay.com/api/open/newPlan/queryFlowRecord.json";

        Map<String, String> queryNewPlanListRequest = new HashMap<>();
        queryNewPlanListRequest.put("caseId", caseId);
        queryNewPlanListRequest.put("ruleId", newPlanRuleId);
        String newPlanListResp = HttpUtil.sendHttpPostCommon(QUERY_NEW_PLAN_RULE_URL, JSONObject.toJSONString(queryNewPlanListRequest));
        JSONObject newPlanListRespJsonObj = JSONObject.parseObject(newPlanListResp);
        JSONArray newPlanList = newPlanListRespJsonObj.getJSONObject("data").getJSONArray("list");
        String caseInstanceId = Optional.ofNullable(newPlanList)
                .orElseGet(JSONArray::new)
                .stream()
                .filter(JSONObject.class::isInstance)
                .map(JSONObject.class::cast)
                .filter(item ->
                        "DEV".equals(item.getString("env")) &&
                                "E2E".equals(item.getString("caseType")) &&
                                "VALID".equals(item.getString("status")))
                .max(Comparator.comparingInt(
                        item -> item.getIntValue("flowFieldCount")))
                .map(item -> item.getString("caseInstanceId"))
                .orElse(null);
        boolean queryNewPlanListStatus = newPlanListRespJsonObj.getBoolean("success");
        result.setQueryNewPlanListStatus(queryNewPlanListStatus);
        result.setCaseInstanceId(caseInstanceId);
        result.setMessage(newPlanListRespJsonObj.getString("message"));
        logger.info("query new Plan List status: {},hit instanceId: {}",queryNewPlanListStatus,caseInstanceId);
        return result;
    }

    @Tool(name="addDependencyRule", description = "Add dependency rule and activate it. Input: caseId (test case ID), caseInstanceId (case instance ID), downStreamInjectType (exception type), relatedMachineGroup (machine group). Output: whether creation was successful")
    public NewTestAddDependencyRuleResult toolsForAddDependencyRule(String caseId, String caseInstanceId, String downStreamInjectType, String relatedMachineGroup) {
        NewTestAddDependencyRuleResult result = new NewTestAddDependencyRuleResult();
        //新增依赖规则并激活
        String ADD_DEPENDENCY_RULE_URL = "http://bklight.alipay.com/api/open/dependence/analysis/addRule.json";

        Map<String, Object> addDependencyRuleRequest = new HashMap<>();
        addDependencyRuleRequest.put("caseId", caseId);
        addDependencyRuleRequest.put("caseType", "E2E");
        addDependencyRuleRequest.put("benchMarkFlowId", "root");
        addDependencyRuleRequest.put("benchMarkCaseInstanceId", caseInstanceId);
        addDependencyRuleRequest.put("downStreamInjectType", downStreamInjectType);
        addDependencyRuleRequest.put("machineGroupEnv", "DEV");
        addDependencyRuleRequest.put("relatedMachineGroup", relatedMachineGroup);
        addDependencyRuleRequest.put("enableAutoActivate",true);
        String dependencyAddRuleResp = HttpUtil.sendHttpPostCommon(ADD_DEPENDENCY_RULE_URL, JSONObject.toJSONString(addDependencyRuleRequest));
        JSONObject dependencyAddRuleRespJsonObj = JSONObject.parseObject(dependencyAddRuleResp);
        boolean addDependencyStatus = dependencyAddRuleRespJsonObj.getBoolean("success");
        result.setAddDependencyStatus(addDependencyStatus);
        result.setAddDependencyStatus(dependencyAddRuleRespJsonObj.getBoolean("message"));
        logger.info("adding dependency rule status: {}" ,addDependencyStatus);
        return result;
    }

    @Tool(name="queryInjectResultList", description = "Query exception injection execution result list. Input: dependencyRuleId (dependency rule ID). Output: query result list with successful injection results")
    public NewTestQueryInjectsResult toolsForQueryInjectResultList(String dependencyRuleId) {
        NewTestQueryInjectsResult result = new NewTestQueryInjectsResult();

        //查询异常注入执行结果列表
        String QUERY_DEPENDENCY_LIST_URL = "http://bklight.alipay.com/api/open/dependence/analysis/queryInjectDetails";

        Map<String, String> queryInjectResultListRequest = new HashMap<>();
        queryInjectResultListRequest.put("id", dependencyRuleId);
        String injectResultListResp = HttpUtil.sendHttpPostCommon(QUERY_DEPENDENCY_LIST_URL, JSONObject.toJSONString(queryInjectResultListRequest));
        JSONObject injectResultListRespJsonObj = JSONObject.parseObject(injectResultListResp);
        JSONArray injectResultList = injectResultListRespJsonObj.getJSONObject("data").getJSONArray("elements");
        boolean queryInjectResultListStatus = injectResultListRespJsonObj.getBoolean("success");
        result.setQueryInjectResultListStatus(queryInjectResultListStatus);
        result.setMessage(injectResultListRespJsonObj.getString("message"));
        logger.info("query Inject Result status:{}",queryInjectResultListStatus);

        // Filter out elements with status=SUCCESS
        JSONArray successArray = injectResultList.stream()
                .filter(obj -> obj instanceof JSONObject)
                .map(obj -> (JSONObject) obj)
                .filter(jsonObj -> {
                    String status = jsonObj.getString("status");
                    return status != null && status.equals("SUCCESS");
                })
                .collect(
                        JSONArray::new,
                        JSONArray::add,
                        JSONArray::addAll
                );
        result.setSuccessDataList(successArray);

        JSONArray waitArray = injectResultList.stream()
                .filter(obj -> obj instanceof JSONObject)
                .map(obj -> (JSONObject) obj)
                .filter(jsonObj -> {
                    String status = jsonObj.getString("status");
                    return status != null && (status.equals("WAITING")||status.equals("EFFECTIVE"));
                })
                .collect(
                        JSONArray::new,
                        JSONArray::add,
                        JSONArray::addAll
                );
        result.setWaitDataList(waitArray);

        JSONArray failArray = injectResultList.stream()
                .filter(obj -> obj instanceof JSONObject)
                .map(obj -> (JSONObject) obj)
                .filter(jsonObj -> {
                    String status = jsonObj.getString("status");
                    return status != null && status.equals("FAILED");
                })
                .collect(
                        JSONArray::new,
                        JSONArray::add,
                        JSONArray::addAll
                );
        result.setFailDataList(failArray);

        // Count elements
        result.setSuccessCount(successArray.size());
        result.setWaitCount(waitArray.size());
        result.setFailCount(failArray.size());

        return result;
    }

    @Tool(name="toolsForAddBakeryCase", description = "add bakery case in order to render end-user pages with mock the response. Input: bklightMockDataList(mocked data list).Output: whether creation was successful.")
    public DependencyAnalyzerResult toolsForAddBakeryCase(List<BklightMockData> bklightMockDataList){
        //解析并添加用例
        DependencyAnalyzerResult dependencyAnalyzerResult = new DependencyAnalyzerResult();
        //组装实验组
        List<DependencyAnalyzerModel> treatmentDependencyAnalyzerModels = new LinkedList<>();

        for (Object bklightMockDataObj : bklightMockDataList) {
            //1.解析bklightMockData
            JSONObject bklightMockDataJsonObj = (JSONObject) bklightMockDataObj;
            BklightMockData bklightMockData = new BklightMockData();
            bklightMockData.setRule_id(bklightMockDataJsonObj.getString("id"));
            bklightMockData.setCase_type(bklightMockDataJsonObj.getString("caseType"));
            bklightMockData.setCase_id(bklightMockDataJsonObj.getString("caseId"));
            bklightMockData.setStandard_instance_id(bklightMockDataJsonObj.getString("benchMarkCaseInstanceId"));
            bklightMockData.setUpper_node_id(bklightMockDataJsonObj.getString("benchMarkFlowId"));
//            bklightMockData.setUpper_node_app(getCellValueAsString(cellMap.get("5")));
//            bklightMockData.setUpper_node_name(getCellValueAsString(cellMap.get("6")));
            bklightMockData.setDown_node_inject_type(bklightMockDataJsonObj.getString("downStreamInjectType"));
            bklightMockData.setGroup_id(bklightMockDataJsonObj.getString("relatedMachineGroup"));
            bklightMockData.setUpper_node_monitor_rule(bklightMockDataJsonObj.getString("machineGroupEnv"));
            bklightMockData.setDown_node_id(bklightMockDataJsonObj.getString("flowId"));
            bklightMockData.setDown_node_service_name(bklightMockDataJsonObj.getString("flowPoint"));
            bklightMockData.setDown_node_app_name(bklightMockDataJsonObj.getString("appName"));
            bklightMockData.setDown_inject_rule(bklightMockDataJsonObj.getString("injectRule"));
            bklightMockData.setDown_inject_type_v2(bklightMockDataJsonObj.getString("injectType"));
            bklightMockData.setDown_error_rule_status(bklightMockDataJsonObj.getString("status"));
//            bklightMockData.setDown_rule_hit_instance_id(getCellValueAsString(cellMap.get("16")));
//            bklightMockData.setDependency_node_init_param(getCellValueAsString(cellMap.get("17")));
            bklightMockData.setDependency_node_rule_inject_param(bklightMockDataJsonObj.getString("comparedFlowPointRequest"));
            bklightMockData.setDependency_node_init_response(bklightMockDataJsonObj.getString("comparedFlowPointResp"));
//            bklightMockData.setDependency_node_rule_inject_response( getCellValueAsString(cellMap.get("20")));
            bklightMockData.setDependency_node_init_error_info(bklightMockDataJsonObj.getString("comparedFlowPointThrowMsg"));
//            bklightMockData.setDependency_node_rule_inject_error_info(getCellValueAsString(cellMap.get("22")));
//            bklightMockData.setInstance_exec_context(getCellValueAsString(cellMap.get("23")));
            bklightMockData.setHit_instance_node_response(bklightMockDataJsonObj.getString("execResp"));
//            logger.info("bklightMockData: {}", com.alibaba.fastjson.JSON.toJSONString(bklightMockData));// 打印bklightMockData
            bklightMockDataList.add(bklightMockData);

            //2.添加bakery用例
            UIMockQueryDetailResult uiMockQueryDetailResult = BakeryToolsUtil.addBakeryCase(bklightMockData, null, true);
            String bakeryUrl = uiMockQueryDetailResult.getUrl();
            String statisticPicUrl = uiMockQueryDetailResult.getThumbnail();//截图+获取静态图片
            logger.info("对照组：bakeryId：{},bakery演示地址bakeryUrl：{},静态页面图片地址statisticPicUrl:{}", uiMockQueryDetailResult.getId(), bakeryUrl, statisticPicUrl);
            DependencyAnalyzerModel dependencyAnalyzerModel = new DependencyAnalyzerModel();
            dependencyAnalyzerModel.setCaseId(uiMockQueryDetailResult.getId());//待替换，当前为查询bakery的id
            dependencyAnalyzerModel.setCaseName(uiMockQueryDetailResult.getName());
            //sceneId=md5("YEB"+"com.alipay.yebtradebff.transferOut.prepare"+"operation=TRANSFER_OUT"+雨燕迭代地址)
            //雨燕迭代地址：“https://yuyan.antfin-inc.com/yuebao/yeb-trade/sprints/S090011007433/overview”取“S090011007433”
//            dependencyAnalyzerModel.setSceneId(sceneId);
//            dependencyAnalyzerModel.setSceneName(sceneName);
            dependencyAnalyzerModel.setAbnormalDetail(bklightMockData.getDown_node_app_name() + "#" + bklightMockData.getDown_node_service_name() + "#" + bklightMockData.getDown_node_inject_type());//下游应用名称#下游节点名称#下游异常注入类型
            dependencyAnalyzerModel.setBakeryUrl(bakeryUrl);
            dependencyAnalyzerModel.setBakeryStaticPicUrl(statisticPicUrl);
            dependencyAnalyzerModel.setControlGroup(false);//是否是对照组，默认为实验组
            //自动判断强弱依赖--能力待搭建
            dependencyAnalyzerModel.setDependencyType(DEPENDENCY_JUDGE_TYPE_STRONG);
            dependencyAnalyzerModel.setJudgmentInfo("");
            dependencyAnalyzerModel.setValid(true);//强弱依赖判断有效性，默认有效
            //
            treatmentDependencyAnalyzerModels.add(dependencyAnalyzerModel);
            logger.info("dependencyAnalyzerModels: {}", com.alibaba.fastjson.JSON.toJSONString(treatmentDependencyAnalyzerModels));// 打印dependencyAnalyzerModels
        }
        dependencyAnalyzerResult.setSuccess(true);
//        dependencyAnalyzerResult.setSceneId(sceneId);
//        dependencyAnalyzerResult.setSceneName(sceneName);
        dependencyAnalyzerResult.setIterateAddress("S090011007433");
//        dependencyAnalyzerResult.setComparisonGroup(dependencyAnalyzerModel_comparison);
        dependencyAnalyzerResult.setTreatmentGroupList(treatmentDependencyAnalyzerModels);
        logger.info("dependencyAnalyzerResult: {}", com.alibaba.fastjson.JSON.toJSONString(dependencyAnalyzerResult));// 打印分析结果
        return dependencyAnalyzerResult;
    }
}
