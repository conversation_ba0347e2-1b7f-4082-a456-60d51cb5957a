package com.alipay.findataquality.service.ai.mcp;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjsonfordrm.JSON;
import com.alipay.findataquality.service.dto.dependencyAnalyzer.EteExecResultDTO;
import com.alipay.findataquality.service.util.EteUtil;
import com.alipay.findataquality.service.util.HttpUtil;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.context.i18n.LocaleContextHolder;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

public class MCPServerBKlightMockTools {

    //新增测新规则
    private static final String ADD_NEW_PLAN_RULE_URL = "http://bklight.alipay.com/api/open/newPlan/addRule.json";
    //查询测新规则列表-获取绘制了链路依赖的规则
    private static final String QUERY_NEW_PLAN_RULE_URL = "http://bklight.alipay.com/api/open/newPlan/queryFlowRecord.json";
    //新增依赖规则并激活
    private static final String ADD_DEPENDENCY_RULE_URL = "http://bklight.alipay.com/api/open/dependence/analysis/addRule.json";
    //查询异常注入执行结果列表
    private static final String QUERY_DEPENDENCY_LIST_URL = "http://bklight.alipay.com/api/open/dependence/analysis/queryInjectDetails";
    //执行ETE结果
    private static final String ETE_EXECUTE_RET_API = "http://*************/model/caseFlowModelRun.json";

    @Tool(name="getCurrentDateTime",description = "Get the current date and time in the user's timezone")
    String getCurrentDateTime() {
        return LocalDateTime.now().atZone(LocaleContextHolder.getTimeZone().toZoneId()).toString();
    }

    @Tool(name="addNewPlan", description = "Add a new test plan rule. Input: caseId (test case ID) and caseName (test case name). Output: newly created test plan rule ID (newPlanRuleId)")
    String toolsForAddNewPlan(String caseId, String caseName) {
        Map<String, String> addNewPlanRuleRequest = new HashMap<>();
        addNewPlanRuleRequest.put("caseType", "E2E");
        addNewPlanRuleRequest.put("caseId", caseId);
        addNewPlanRuleRequest.put("caseName", caseName);
        addNewPlanRuleRequest.put("env", "DEV");
        String newPlanAddRuleResp = HttpUtil.sendHttpPostCommon(ADD_NEW_PLAN_RULE_URL, JSONObject.toJSONString(addNewPlanRuleRequest));
        JSONObject newPlanAddRuleRespJsonObj = JSONObject.parseObject(newPlanAddRuleResp);
        String newPlanRuleId = newPlanAddRuleRespJsonObj.getJSONObject("data").getString("ruleId");
        boolean addNewPlanRuleStatus = newPlanAddRuleRespJsonObj.getJSONObject("data").getBoolean("success");
        System.out.println("adding new Plan Rule status: " + addNewPlanRuleStatus+",new plan rule id: "+newPlanRuleId);
        return newPlanRuleId;
    }

    @Tool(name="triggerEte", description = "Trigger ETE (End-to-End Test Engine) execution. Input: caseId (test case ID). Output: whether ETE execution was triggered successfully")
    String toolsForTriggerEte(String caseId) {
        Map<String, Object> requestParams = new HashMap<>();
        Map<String, Object> environmentVariables = new HashMap<>();
        environmentVariables.put("env", "dev");
        requestParams.put("environmentVariables", environmentVariables);

        Map<String, String> context = new HashMap<>();
        context.put("flowInstanceCode", caseId);
        requestParams.put("context", context);
        String req = JSON.toJSONString(requestParams);
        EteExecResultDTO triggerETEResp = EteUtil.executeEteTest(ETE_EXECUTE_RET_API, req);
        System.out.println("trigger ETE resp: " + JSON.toJSONString(triggerETEResp));
        boolean eteExecuteSuccess = triggerETEResp.getSuccess();
        System.out.println("trigger ETE status: " + eteExecuteSuccess);
        return String.valueOf(eteExecuteSuccess);
    }

    @Tool(name="queryNewPlanList", description = "Query new plan traffic list. Input: caseId (test case ID) and newPlanRuleId (new plan rule ID). Output: caseInstanceId with the most dependency nodes")
    String toolsForQueryNewPlanList(String caseId, String newPlanRuleId) {
        Map<String, String> queryNewPlanListRequest = new HashMap<>();
        queryNewPlanListRequest.put("caseId", caseId);
        queryNewPlanListRequest.put("ruleId", newPlanRuleId);
        System.out.println("queryNewPlanListRequest:"+JSON.toJSONString(queryNewPlanListRequest));
        String newPlanListResp = HttpUtil.sendHttpPostCommon(QUERY_NEW_PLAN_RULE_URL, JSONObject.toJSONString(queryNewPlanListRequest));
        System.out.println("newPlanListResp:"+newPlanListResp);
        JSONObject newPlanListRespJsonObj = JSONObject.parseObject(newPlanListResp);
        JSONArray newPlanList = newPlanListRespJsonObj.getJSONObject("data").getJSONArray("list");
        String caseInstanceId = Optional.ofNullable(newPlanList)
                .orElseGet(JSONArray::new)
                .stream()
                .filter(JSONObject.class::isInstance)
                .map(JSONObject.class::cast)
                .filter(item ->
                        "DEV".equals(item.getString("env")) &&
                                "E2E".equals(item.getString("caseType")) &&
                                "VALID".equals(item.getString("status")))
                .max(Comparator.comparingInt(
                        item -> item.getIntValue("flowFieldCount")))
                .map(item -> item.getString("caseInstanceId"))
                .orElse(null);
        boolean queryNewPlanListStatus = newPlanListRespJsonObj.getBoolean("success");
        System.out.println("query new Plan List status: " + queryNewPlanListStatus+",new Plan List:"+JSON.toJSONString(caseInstanceId));
        return caseInstanceId;
    }

    @Tool(name="addDependencyRule", description = "Add dependency rule and activate it. Input: caseId (test case ID), caseInstanceId (case instance ID), downStreamInjectType (exception type), relatedMachineGroup (machine group). Output: whether creation was successful")
    String toolsForAddDependencyRule(String caseId, String caseInstanceId, String downStreamInjectType, String relatedMachineGroup) {
        Map<String, Object> addDependencyRuleRequest = new HashMap<>();
        addDependencyRuleRequest.put("caseId", caseId);
        addDependencyRuleRequest.put("caseType", "E2E");
        addDependencyRuleRequest.put("benchMarkFlowId", "root");
        addDependencyRuleRequest.put("benchMarkCaseInstanceId", caseInstanceId);
        addDependencyRuleRequest.put("downStreamInjectType", downStreamInjectType);
        addDependencyRuleRequest.put("machineGroupEnv", "DEV");
        addDependencyRuleRequest.put("relatedMachineGroup", relatedMachineGroup);
        addDependencyRuleRequest.put("enableAutoActivate",true);
        String dependencyAddRuleResp = HttpUtil.sendHttpPostCommon(ADD_DEPENDENCY_RULE_URL, JSONObject.toJSONString(addDependencyRuleRequest));
        System.out.println("dependencyAddRuleResp: " + dependencyAddRuleResp);
        JSONObject dependencyAddRuleRespJsonObj = JSONObject.parseObject(dependencyAddRuleResp);
        boolean addDependencyStatus = dependencyAddRuleRespJsonObj.getBoolean("success");
        System.out.println("adding dependency rule status: " + addDependencyStatus);
        return String.valueOf(addDependencyStatus);
    }

    @Tool(name="queryInjectResultList", description = "Query exception injection execution result list. Input: dependencyRuleId (dependency rule ID). Output: query result list with successful injection results")
    String toolsForQueryInjectResultList(String dependencyRuleId) {
        Map<String, String> queryInjectResultListRequest = new HashMap<>();
        queryInjectResultListRequest.put("id", dependencyRuleId);
        String injectResultListResp = HttpUtil.sendHttpPostCommon(QUERY_DEPENDENCY_LIST_URL, JSONObject.toJSONString(queryInjectResultListRequest));
        System.out.println("injectResultListResp:"+injectResultListResp);
        JSONObject injectResultListRespJsonObj = JSONObject.parseObject(injectResultListResp);
        JSONArray injectResultList = injectResultListRespJsonObj.getJSONObject("data").getJSONArray("elements");
        System.out.println("injectResultList:"+JSON.toJSONString(injectResultList));
        boolean queryInjectResultListStatus = injectResultListRespJsonObj.getBoolean("success");
        System.out.println("query Inject Result status:"+queryInjectResultListStatus+",List: " + JSON.toJSONString(injectResultList));

        // Filter out elements with status=SUCCESS
        JSONArray successArray = injectResultList.stream()
                .filter(obj -> obj instanceof JSONObject)
                .map(obj -> (JSONObject) obj)
                .filter(jsonObj -> {
                    String status = jsonObj.getString("status");
                    return status != null && status.equals("SUCCESS");
                })
                .collect(
                        JSONArray::new,
                        JSONArray::add,
                        JSONArray::addAll
                );

        // Count non-SUCCESS elements
        int nonSuccessCount = injectResultList.size() - successArray.size();
        System.out.println("query Inject Result nonSuccessCount:"+nonSuccessCount);

        return JSON.toJSONString(successArray);
    }

}
