package com.alipay.findataquality.service.ai.agent;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * BKlight Mock Agent Controller
 * 提供RESTful API接口来调用BKlight Mock Agent服务
 */
@RestController
@RequestMapping("/api/bklight/agent")
public class BklightMockAgentController {

    @Autowired(required = false)
    private BklightMockAgentService bklightMockAgentService;

    /**
     * 智能执行BKlight Mock流程（推荐使用）
     * 根据用户上下文和当前状态智能决定执行步骤
     *
     * @param request 请求参数
     * @return 执行结果
     */
    @PostMapping("/smart-execute")
    public String smartExecute(@RequestBody SmartBklightMockRequest request) {
        return bklightMockAgentService.executeSmartBklightMockFlow(
            request.getUserId(),
            request.getCaseId(),
            request.getCaseName(),
            request.getDownStreamInjectType(),
            request.getRelatedMachineGroup()
        );
    }


    /**
     * 智能聊天接口（推荐使用）
     * 根据用户上下文智能响应
     *
     * @param userId 用户ID
     * @param userInput 用户输入
     * @return Agent响应
     */
    @PostMapping("/smart-chat")
    public String smartChat(@RequestParam String userId, @RequestParam String userInput) {
        return bklightMockAgentService.smartChatWithContext(userId, userInput);
    }

    /**
     * 执行完整的BKlight Mock流程（原有方法，保持兼容性）
     *
     * @param request 请求参数
     * @return 执行结果
     */
    @PostMapping("/execute-full-flow")
    public String executeFullFlow(@RequestBody SmartBklightMockRequest request) {
        return bklightMockAgentService.executeFullBklightMockFlow(
            request.getCaseId(),
            request.getCaseName(),
            request.getDownStreamInjectType(),
            request.getRelatedMachineGroup()
        );
    }

    /**
     * 执行特定步骤
     *
     * @param userId 用户ID
     * @param stepNumber 步骤号
     * @return 执行结果
     */
    @PostMapping("/execute-specific-step")
    public String executeSpecificStep(@RequestParam String userId, @RequestParam int stepNumber) {
        return bklightMockAgentService.executeSpecificStep(userId, stepNumber);
    }

    /**
     * 执行单个步骤
     * 
     * @param stepName 步骤名称
     * @param parameters 参数
     * @return 执行结果
     */
    @PostMapping("/execute-step")
    public String executeStep(@RequestParam String stepName, @RequestParam String parameters) {
        return bklightMockAgentService.executeSingleStep(stepName, parameters);
    }


    /**
     * 自定义Agent交互（原有方法，保持兼容性）
     *
     * @param prompt 自定义提示
     * @return Agent响应
     */
    @PostMapping("/chat")
    public String chat(@RequestParam String prompt) {
        return bklightMockAgentService.chatWithMcp(prompt);
    }

    /**
     * 获取用户执行状态
     *
     * @param userId 用户ID
     * @return 执行状态
     */
    @GetMapping("/status")
    public String getUserStatus(@RequestParam String userId) {
        return bklightMockAgentService.getUserExecutionStatus(userId);
    }

    /**
     * 清除用户上下文
     *
     * @param userId 用户ID
     * @return 操作结果
     */
    @PostMapping("/clear-context")
    public String clearUserContext(@RequestParam String userId) {
        bklightMockAgentService.clearUserContext(userId);
        return "User context cleared for: " + userId;
    }

    /**
     * 获取可用工具列表
     * 
     * @return 工具列表
     */
    @GetMapping("/tools")
    public String getAvailableTools() {
        return bklightMockAgentService.getAvailableTools();
    }

    /**
     * 执行自定义工具链
     * 
     * @param customPrompt 自定义提示
     * @return 执行结果
     */
    @PostMapping("/custom-chain")
    public String executeCustomChain(@RequestParam String customPrompt) {
        return bklightMockAgentService.executeCustomToolChain(customPrompt);
    }

    /**
     * 智能BKlight Mock请求参数类
     */
    public static class SmartBklightMockRequest {
        private String userId;
        private String caseId;
        private String caseName;
        private String downStreamInjectType;
        private String relatedMachineGroup;

        // Getters and Setters
        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }
        public String getCaseId() { return caseId; }
        public void setCaseId(String caseId) { this.caseId = caseId; }
        public String getCaseName() { return caseName; }
        public void setCaseName(String caseName) { this.caseName = caseName; }
        public String getDownStreamInjectType() { return downStreamInjectType; }
        public void setDownStreamInjectType(String downStreamInjectType) { this.downStreamInjectType = downStreamInjectType; }
        public String getRelatedMachineGroup() { return relatedMachineGroup; }
        public void setRelatedMachineGroup(String relatedMachineGroup) { this.relatedMachineGroup = relatedMachineGroup; }
    }

}
