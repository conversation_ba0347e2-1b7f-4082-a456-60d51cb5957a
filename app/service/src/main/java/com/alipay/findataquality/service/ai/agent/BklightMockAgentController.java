package com.alipay.findataquality.service.ai.agent;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * BKlight Mock Agent Controller
 * 提供RESTful API接口来调用BKlight Mock Agent服务
 */
@RestController
@RequestMapping("/api/bklight/agent")
public class BklightMockAgentController {

    @Autowired
    private BklightMockAgentService bklightMockAgentService;

    /**
     * 执行完整的BKlight Mock流程
     * 
     * @param request 请求参数
     * @return 执行结果
     */
    @PostMapping("/execute-full-flow")
    public String executeFullFlow(@RequestBody BklightMockRequest request) {
        return bklightMockAgentService.executeFullBklightMockFlow(
            request.getCaseId(),
            request.getCaseName(),
            request.getDownStreamInjectType(),
            request.getRelatedMachineGroup()
        );
    }

    /**
     * 执行单个步骤
     * 
     * @param stepName 步骤名称
     * @param parameters 参数
     * @return 执行结果
     */
    @PostMapping("/execute-step")
    public String executeStep(@RequestParam String stepName, @RequestParam String parameters) {
        return bklightMockAgentService.executeSingleStep(stepName, parameters);
    }

    /**
     * 自定义Agent交互
     * 
     * @param prompt 自定义提示
     * @return Agent响应
     */
    @PostMapping("/chat")
    public String chat(@RequestParam String prompt) {
        return bklightMockAgentService.chatWithMcp(prompt);
    }

    /**
     * 获取可用工具列表
     * 
     * @return 工具列表
     */
    @GetMapping("/tools")
    public String getAvailableTools() {
        return bklightMockAgentService.getAvailableTools();
    }

    /**
     * 执行自定义工具链
     * 
     * @param customPrompt 自定义提示
     * @return 执行结果
     */
    @PostMapping("/custom-chain")
    public String executeCustomChain(@RequestParam String customPrompt) {
        return bklightMockAgentService.executeCustomToolChain(customPrompt);
    }

    /**
     * BKlight Mock请求参数类
     */
    public static class BklightMockRequest {
        private String caseId;
        private String caseName;
        private String downStreamInjectType;
        private String relatedMachineGroup;

        // Getters and Setters
        public String getCaseId() {
            return caseId;
        }

        public void setCaseId(String caseId) {
            this.caseId = caseId;
        }

        public String getCaseName() {
            return caseName;
        }

        public void setCaseName(String caseName) {
            this.caseName = caseName;
        }

        public String getDownStreamInjectType() {
            return downStreamInjectType;
        }

        public void setDownStreamInjectType(String downStreamInjectType) {
            this.downStreamInjectType = downStreamInjectType;
        }

        public String getRelatedMachineGroup() {
            return relatedMachineGroup;
        }

        public void setRelatedMachineGroup(String relatedMachineGroup) {
            this.relatedMachineGroup = relatedMachineGroup;
        }
    }
}
