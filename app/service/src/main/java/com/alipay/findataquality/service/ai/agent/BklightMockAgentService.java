package com.alipay.findataquality.service.ai.agent;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.sofa.ai.antllm.AntLLMChatModel;
import com.alipay.sofa.ai.antllm.AntLLMChatOptions;
import com.alipay.sofa.ai.mcp.SofaMcpToolCallbackProvider;
import com.alipay.sofa.ai.mcp.client.SofaMcpClientFactory;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * BKlight Mock Agent Service
 * 提供串联多个tools的Agent能力，通过MCP协议进行工具调用
 * 支持上下文感知和智能步骤管理
 */
@Service
public class BklightMockAgentService {

    @Autowired(required = false)
    private SofaMcpClientFactory sofaMcpClientFactory;

    @Autowired(required = false)
    private AntLLMChatModel antLLMChatModel;

    // 用户会话上下文存储
    private final Map<String, ExecutionContext> userContexts = new ConcurrentHashMap<>();

    /**
     * 执行上下文类
     */
    public static class ExecutionContext {
        private String caseId;
        private String caseName;
        private String downStreamInjectType;
        private String relatedMachineGroup;
        private String newPlanRuleId;
        private String caseInstanceId;
        private String dependencyRuleId;
        private int currentStep = 0;
        private boolean step1Completed = false;
        private boolean step2Completed = false;
        private boolean step3Completed = false;
        private boolean step4Completed = false;
        private boolean step5Completed = false;
        private boolean hasWaitingInjections = false;
        private long lastWaitPromptTime = 0;

        // Getters and Setters
        public String getCaseId() { return caseId; }
        public void setCaseId(String caseId) { this.caseId = caseId; }
        public String getCaseName() { return caseName; }
        public void setCaseName(String caseName) { this.caseName = caseName; }
        public String getDownStreamInjectType() { return downStreamInjectType; }
        public void setDownStreamInjectType(String downStreamInjectType) { this.downStreamInjectType = downStreamInjectType; }
        public String getRelatedMachineGroup() { return relatedMachineGroup; }
        public void setRelatedMachineGroup(String relatedMachineGroup) { this.relatedMachineGroup = relatedMachineGroup; }
        public String getNewPlanRuleId() { return newPlanRuleId; }
        public void setNewPlanRuleId(String newPlanRuleId) { this.newPlanRuleId = newPlanRuleId; }
        public String getCaseInstanceId() { return caseInstanceId; }
        public void setCaseInstanceId(String caseInstanceId) { this.caseInstanceId = caseInstanceId; }
        public String getDependencyRuleId() { return dependencyRuleId; }
        public void setDependencyRuleId(String dependencyRuleId) { this.dependencyRuleId = dependencyRuleId; }
        public int getCurrentStep() { return currentStep; }
        public void setCurrentStep(int currentStep) { this.currentStep = currentStep; }
        public boolean isStep1Completed() { return step1Completed; }
        public void setStep1Completed(boolean step1Completed) { this.step1Completed = step1Completed; }
        public boolean isStep2Completed() { return step2Completed; }
        public void setStep2Completed(boolean step2Completed) { this.step2Completed = step2Completed; }
        public boolean isStep3Completed() { return step3Completed; }
        public void setStep3Completed(boolean step3Completed) { this.step3Completed = step3Completed; }
        public boolean isStep4Completed() { return step4Completed; }
        public void setStep4Completed(boolean step4Completed) { this.step4Completed = step4Completed; }
        public boolean isStep5Completed() { return step5Completed; }
        public void setStep5Completed(boolean step5Completed) { this.step5Completed = step5Completed; }
        public boolean isHasWaitingInjections() { return hasWaitingInjections; }
        public void setHasWaitingInjections(boolean hasWaitingInjections) { this.hasWaitingInjections = hasWaitingInjections; }
        public long getLastWaitPromptTime() { return lastWaitPromptTime; }
        public void setLastWaitPromptTime(long lastWaitPromptTime) { this.lastWaitPromptTime = lastWaitPromptTime; }
    }

    /**
     * 智能执行BKlight Mock流程
     * 根据用户上下文和当前状态决定执行哪些步骤
     *
     * @param userId 用户ID
     * @param caseId 用例ID
     * @param caseName 用例名称
     * @param downStreamInjectType 异常类型
     * @param relatedMachineGroup 机器组
     * @return 执行结果
     */
    public String executeSmartBklightMockFlow(String userId, String caseId, String caseName,
                                            String downStreamInjectType, String relatedMachineGroup) {

        ExecutionContext context = getOrCreateContext(userId);

        // 设置基本参数
        context.setCaseId(caseId);
        context.setCaseName(caseName);
        context.setDownStreamInjectType(downStreamInjectType);
        context.setRelatedMachineGroup(relatedMachineGroup);

        // 构建智能提示
        String prompt = buildSmartPrompt(context);

        return chatWithMcp(prompt);
    }

    /**
     * 执行特定步骤
     *
     * @param userId 用户ID
     * @param stepNumber 步骤号
     * @return 执行结果
     */
    public String executeSpecificStep(String userId, int stepNumber) {
        ExecutionContext context = getOrCreateContext(userId);

        String prompt = buildStepSpecificPrompt(context, stepNumber);

        return chatWithMcp(prompt);
    }

    /**
     * 获取或创建用户上下文
     */
    private ExecutionContext getOrCreateContext(String userId) {
        return userContexts.computeIfAbsent(userId, k -> new ExecutionContext());
    }

    /**
     * 构建智能提示
     */
    private String buildSmartPrompt(ExecutionContext context) {
        StringBuilder prompt = new StringBuilder();

        prompt.append("You are a BKlight Mock testing assistant. Based on the current execution context, please execute the appropriate steps.\n\n");

        // 添加参数信息
        prompt.append("Parameters:\n");
        prompt.append("- caseId: ").append(context.getCaseId()).append("\n");
        prompt.append("- caseName: ").append(context.getCaseName()).append("\n");
        prompt.append("- downStreamInjectType: ").append(context.getDownStreamInjectType()).append("\n");
        prompt.append("- relatedMachineGroup: ").append(context.getRelatedMachineGroup()).append("\n\n");

        // 分析当前状态并决定执行策略
        if (!context.isStep1Completed()) {
            prompt.append("Current Status: No previous execution found. Starting from Step 1.\n\n");
            prompt.append(getStep1Instructions());
        } else if (!context.isStep2Completed()) {
            prompt.append("Current Status: Step 1 completed, but Step 2 failed or not executed. Re-executing from Step 2.\n\n");
            prompt.append(getStep2Instructions(context));
        } else if (!context.isStep3Completed()) {
            prompt.append("Current Status: Steps 1-2 completed, but Step 3 failed or not executed. Re-executing from Step 3.\n\n");
            prompt.append(getStep3Instructions(context));
        } else if (!context.isStep4Completed()) {
            prompt.append("Current Status: Steps 1-3 completed, but Step 4 failed or not executed. Re-executing from Step 4.\n\n");
            prompt.append(getStep4Instructions(context));
        } else if (!context.isStep5Completed() || context.isHasWaitingInjections()) {
            prompt.append("Current Status: Steps 1-4 completed. Executing Step 5 or Step 6 (re-check waiting injections).\n\n");
            prompt.append(getStep5Instructions(context));
        } else {
            prompt.append("Current Status: All steps completed successfully.\n\n");
            prompt.append("Please provide a summary of the completed execution.");
        }

        return prompt.toString();
    }

    /**
     * 构建特定步骤的提示
     */
    private String buildStepSpecificPrompt(ExecutionContext context, int stepNumber) {
        StringBuilder prompt = new StringBuilder();

        prompt.append("You are a BKlight Mock testing assistant. Please execute Step ").append(stepNumber).append(".\n\n");

        // 添加参数信息
        if (context.getCaseId() != null) {
            prompt.append("Parameters:\n");
            prompt.append("- caseId: ").append(context.getCaseId()).append("\n");
            prompt.append("- caseName: ").append(context.getCaseName()).append("\n");
            prompt.append("- downStreamInjectType: ").append(context.getDownStreamInjectType()).append("\n");
            prompt.append("- relatedMachineGroup: ").append(context.getRelatedMachineGroup()).append("\n");
            if (context.getNewPlanRuleId() != null) {
                prompt.append("- newPlanRuleId: ").append(context.getNewPlanRuleId()).append("\n");
            }
            if (context.getCaseInstanceId() != null) {
                prompt.append("- caseInstanceId: ").append(context.getCaseInstanceId()).append("\n");
            }
            if (context.getDependencyRuleId() != null) {
                prompt.append("- dependencyRuleId: ").append(context.getDependencyRuleId()).append("\n");
            }
            prompt.append("\n");
        }

        switch (stepNumber) {
            case 1:
                prompt.append(getStep1Instructions());
                break;
            case 2:
                prompt.append(getStep2Instructions(context));
                break;
            case 3:
                prompt.append(getStep3Instructions(context));
                break;
            case 4:
                prompt.append(getStep4Instructions(context));
                break;
            case 5:
            case 6:
                prompt.append(getStep5Instructions(context));
                break;
            default:
                prompt.append("Invalid step number. Please specify a step between 1-6.");
        }

        return prompt.toString();
    }

    /**
     * 步骤1指令：增加测新规则
     */
    private String getStep1Instructions() {
        return "STEP 1: Add New Test Plan Rule\n" +
               "Use the addNewPlan tool with caseId and caseName parameters.\n" +
               "Success Criteria: newPlanRuleId is not null\n" +
               "If successful, proceed to Step 2. If failed, return error with detailed reason.\n" +
               "After execution, update the context to mark Step 1 as completed if successful.\n\n" +
               "Execute: addNewPlan(caseId, caseName)\n";
    }

    /**
     * 步骤2指令：触发ETE执行
     */
    private String getStep2Instructions(ExecutionContext context) {
        return "STEP 2: Trigger ETE Execution\n" +
               "Use the triggerEte tool with caseId parameter.\n" +
               "Success Criteria: eteExecuteSuccess is true\n" +
               "If successful, proceed to Step 3. If failed, return error with detailed reason.\n" +
               "After execution, update the context to mark Step 2 as completed if successful.\n\n" +
               "Execute: triggerEte(caseId)\n";
    }

    /**
     * 步骤3指令：查询测新流量列表
     */
    private String getStep3Instructions(ExecutionContext context) {
        return "STEP 3: Query New Plan Traffic List\n" +
               "Use the queryNewPlanList tool with caseId and newPlanRuleId parameters.\n" +
               "Success Criteria: Find caseInstanceId with flowFieldCount > 0 (preferably > 10)\n" +
               "Select the caseInstanceId with the highest flowFieldCount from data.list.\n" +
               "If no suitable caseInstanceId found, loop back to Step 2 until complete dependency tree is found.\n" +
               "If successful, proceed to Step 4. If failed, return error with detailed reason.\n" +
               "After execution, update the context to mark Step 3 as completed if successful.\n\n" +
               "Execute: queryNewPlanList(caseId, newPlanRuleId)\n";
    }

    /**
     * 步骤4指令：新增依赖配置规则
     */
    private String getStep4Instructions(ExecutionContext context) {
        return "STEP 4: Add Dependency Configuration Rule\n" +
               "Use the addDependencyRule tool with caseId, caseInstanceId, downStreamInjectType, and relatedMachineGroup parameters.\n" +
               "Success Criteria: addDependencyStatus is true\n" +
               "If successful, proceed to Step 5. If failed, return error with detailed reason.\n" +
               "After execution, update the context to mark Step 4 as completed if successful.\n\n" +
               "Execute: addDependencyRule(caseId, caseInstanceId, downStreamInjectType, relatedMachineGroup)\n";
    }

    /**
     * 步骤5/6指令：查询异常注入执行结果列表
     */
    private String getStep5Instructions(ExecutionContext context) {
        StringBuilder instructions = new StringBuilder();

        if (context.isHasWaitingInjections()) {
            instructions.append("STEP 6: Re-check Injection Results (Follow-up)\n");
            instructions.append("This is a follow-up check for previously waiting injections.\n");
        } else {
            instructions.append("STEP 5: Query Exception Injection Execution Results\n");
        }

        instructions.append("Use the queryInjectResultList tool with dependencyRuleId parameter.\n");
        instructions.append("Analyze the results based on status distribution:\n\n");

        instructions.append("1) If all status are 'WAIT': Injection not started yet\n");
        instructions.append("   - Return all results with waiting count\n");
        instructions.append("   - Prompt: '注入尚未开始，需要再等待10-30分钟再单独执行该步骤'\n\n");

        instructions.append("2) If 'WAIT' count > ('FAIL' + 'SUCCESS') count: Still mostly waiting\n");
        instructions.append("   - Return all results with waiting count\n");
        instructions.append("   - Prompt: '需要再等待10-30分钟再单独执行该步骤'\n\n");

        instructions.append("3) If all status are 'FAIL': Complete failure\n");
        instructions.append("   - Return error in Step 5\n");
        instructions.append("   - Reason: '注入失败，需bklight人员排查'\n\n");

        instructions.append("4) If 'SUCCESS' count > ('FAIL' + 'WAIT') count: Mostly successful\n");
        instructions.append("   - Return all results with FAIL/WAIT/SUCCESS counts and ratios\n");
        instructions.append("   - For WAIT status: '需要再等待10-30分钟再单独执行该步骤'\n\n");

        instructions.append("Execute: queryInjectResultList(dependencyRuleId)\n");

        return instructions.toString();
    }

    /**
     * 更新执行上下文
     *
     * @param userId 用户ID
     * @param stepNumber 完成的步骤号
     * @param resultData 步骤执行结果数据
     */
    public void updateExecutionContext(String userId, int stepNumber, Map<String, Object> resultData) {
        ExecutionContext context = getOrCreateContext(userId);

        switch (stepNumber) {
            case 1:
                context.setStep1Completed(true);
                if (resultData.containsKey("newPlanRuleId")) {
                    context.setNewPlanRuleId((String) resultData.get("newPlanRuleId"));
                }
                break;
            case 2:
                context.setStep2Completed(true);
                break;
            case 3:
                context.setStep3Completed(true);
                if (resultData.containsKey("caseInstanceId")) {
                    context.setCaseInstanceId((String) resultData.get("caseInstanceId"));
                }
                break;
            case 4:
                context.setStep4Completed(true);
                if (resultData.containsKey("dependencyRuleId")) {
                    context.setDependencyRuleId((String) resultData.get("dependencyRuleId"));
                }
                break;
            case 5:
                context.setStep5Completed(true);
                if (resultData.containsKey("hasWaitingInjections")) {
                    context.setHasWaitingInjections((Boolean) resultData.get("hasWaitingInjections"));
                    if (context.isHasWaitingInjections()) {
                        context.setLastWaitPromptTime(System.currentTimeMillis());
                    }
                }
                break;
        }

        context.setCurrentStep(stepNumber);
    }

    /**
     * 获取用户执行状态
     *
     * @param userId 用户ID
     * @return 执行状态描述
     */
    public String getUserExecutionStatus(String userId) {
        ExecutionContext context = userContexts.get(userId);
        if (context == null) {
            return "No execution context found for user: " + userId;
        }

        StringBuilder status = new StringBuilder();
        status.append("Execution Status for User: ").append(userId).append("\n");
        status.append("Current Step: ").append(context.getCurrentStep()).append("\n");
        status.append("Step 1 (Add New Plan): ").append(context.isStep1Completed() ? "✓ Completed" : "✗ Not Completed").append("\n");
        status.append("Step 2 (Trigger ETE): ").append(context.isStep2Completed() ? "✓ Completed" : "✗ Not Completed").append("\n");
        status.append("Step 3 (Query Plan List): ").append(context.isStep3Completed() ? "✓ Completed" : "✗ Not Completed").append("\n");
        status.append("Step 4 (Add Dependency Rule): ").append(context.isStep4Completed() ? "✓ Completed" : "✗ Not Completed").append("\n");
        status.append("Step 5 (Query Inject Results): ").append(context.isStep5Completed() ? "✓ Completed" : "✗ Not Completed").append("\n");

        if (context.isHasWaitingInjections()) {
            long waitTime = (System.currentTimeMillis() - context.getLastWaitPromptTime()) / (1000 * 60);
            status.append("Has Waiting Injections: Yes (").append(waitTime).append(" minutes ago)\n");
        }

        return status.toString();
    }

    /**
     * 清除用户上下文
     *
     * @param userId 用户ID
     */
    public void clearUserContext(String userId) {
        userContexts.remove(userId);
    }

    /**
     * 智能聊天接口 - 根据用户输入和上下文智能响应
     *
     * @param userId 用户ID
     * @param userInput 用户输入
     * @return Agent响应
     */
    public String smartChatWithContext(String userId, String userInput) {
        ExecutionContext context = getOrCreateContext(userId);

        // 使用大模型分析用户输入意图
        String intent = analyzeUserIntent(userInput, context);

        StringBuilder prompt = new StringBuilder();
        prompt.append("You are a BKlight Mock testing assistant. ");
        prompt.append("User Intent Analysis: ").append(intent).append("\n");
        prompt.append("Original User Input: ").append(userInput).append("\n\n");

        // 根据意图和上下文构建智能响应
        if (intent.equals("start_new_flow")) {
            prompt.append("User wants to start a completely new testing flow. ");
            // 清除之前的上下文，重新开始
            clearUserContext(userId);
            context = getOrCreateContext(userId);
            prompt.append(buildSmartPrompt(context));

        } else if (intent.equals("continue_flow")) {
            prompt.append("User wants to continue the existing flow from current state. ");
            prompt.append(buildSmartPrompt(context));

        } else if (intent.equals("check_status")) {
            prompt.append("User wants to check current execution status. ");
            prompt.append("Please provide a friendly summary of the current status:\n");
            prompt.append(getUserExecutionStatus(userId));
            prompt.append("\nProvide suggestions for next steps if applicable.");

        } else if (intent.startsWith("retry_step")) {
            // 处理重试特定步骤
            int stepNumber = extractStepNumberFromIntent(intent);
            if (stepNumber == 0) {
                stepNumber = extractStepNumber(userInput); // 回退到原有方法
            }

            if (stepNumber > 0) {
                prompt.append("User wants to retry step ").append(stepNumber).append(". ");
                // 重置该步骤及后续步骤的状态
                resetStepsFromNumber(context, stepNumber);
                prompt.append(buildStepSpecificPrompt(context, stepNumber));
            } else {
                prompt.append("User wants to retry some step but didn't specify which one. ");
                prompt.append("Please ask the user to specify which step to retry (1-6). ");
                prompt.append("Current status:\n").append(getUserExecutionStatus(userId));
            }

        } else if (intent.equals("wait_check")) {
            prompt.append("User is checking waiting injections after waiting period. ");
            prompt.append("This is a follow-up check for Step 5/6. ");
            prompt.append(getStep5Instructions(context));

        } else if (intent.equals("clear_restart")) {
            prompt.append("User wants to clear context and restart. ");
            clearUserContext(userId);
            prompt.append("Context cleared. Please provide parameters to start a new testing flow, ");
            prompt.append("or ask the user for caseId, caseName, downStreamInjectType, and relatedMachineGroup.");

        } else if (intent.equals("help_info")) {
            prompt.append("User needs help or information about the system. ");
            prompt.append("Please provide helpful information about BKlight Mock testing process, ");
            prompt.append("available commands, and current status. ");
            prompt.append("Current context: ").append(getUserExecutionStatus(userId));

        } else {
            // general_query 或其他未识别意图
            prompt.append("General query or unclear intent. ");
            prompt.append("Please provide helpful information and try to understand what the user wants. ");
            prompt.append("Current context: ").append(getUserExecutionStatus(userId));
            prompt.append("\nSuggest possible actions the user might want to take.");
        }

        return chatWithMcp(prompt.toString());
    }

    /**
     * 使用大模型智能分析用户输入意图
     */
    private String analyzeUserIntent(String userInput, ExecutionContext context) {
        // 构建意图分析的提示词
        StringBuilder intentPrompt = new StringBuilder();
        intentPrompt.append("You are an intent analysis assistant for BKlight Mock testing system. ");
        intentPrompt.append("Analyze the user input and determine the intent category.\n\n");

        // 添加上下文信息
        intentPrompt.append("Current Context:\n");
        intentPrompt.append("- Current Step: ").append(context.getCurrentStep()).append("\n");
        intentPrompt.append("- Step 1 Completed: ").append(context.isStep1Completed()).append("\n");
        intentPrompt.append("- Step 2 Completed: ").append(context.isStep2Completed()).append("\n");
        intentPrompt.append("- Step 3 Completed: ").append(context.isStep3Completed()).append("\n");
        intentPrompt.append("- Step 4 Completed: ").append(context.isStep4Completed()).append("\n");
        intentPrompt.append("- Step 5 Completed: ").append(context.isStep5Completed()).append("\n");
        intentPrompt.append("- Has Waiting Injections: ").append(context.isHasWaitingInjections()).append("\n\n");

        // 定义意图分类
        intentPrompt.append("Intent Categories:\n");
        intentPrompt.append("1. start_new_flow: User wants to start a new testing flow from beginning\n");
        intentPrompt.append("2. continue_flow: User wants to continue existing flow from current state\n");
        intentPrompt.append("3. check_status: User wants to check current execution status\n");
        intentPrompt.append("4. retry_step: User wants to retry a specific step (may include step number)\n");
        intentPrompt.append("5. wait_check: User wants to check waiting injections after waiting period\n");
        intentPrompt.append("6. clear_restart: User wants to clear context and restart\n");
        intentPrompt.append("7. help_info: User needs help or information about the system\n");
        intentPrompt.append("8. general_query: General questions or unclear intent\n\n");

        // 添加用户输入
        intentPrompt.append("User Input: \"").append(userInput).append("\"\n\n");

        // 要求返回格式
        intentPrompt.append("Please analyze the user input and return ONLY the intent category name ");
        intentPrompt.append("(one of the 8 categories above). If the user mentions a specific step number, ");
        intentPrompt.append("append it like 'retry_step:3'. Consider both Chinese and English inputs.\n\n");
        intentPrompt.append("Response format: [intent_category] or [intent_category:step_number]");

        try {
            // 调用大模型进行意图分析
            String intentResult = callLLMForIntentAnalysis(intentPrompt.toString());

            // 解析并验证结果
            return parseAndValidateIntent(intentResult, context);

        } catch (Exception e) {
            // 如果大模型调用失败，回退到简单规则匹配
            System.err.println("LLM intent analysis failed, falling back to rule-based: " + e.getMessage());
            return fallbackIntentAnalysis(userInput, context);
        }
    }

    /**
     * 调用大模型进行意图分析
     */
    private String callLLMForIntentAnalysis(String prompt) {
        if (sofaMcpClientFactory == null || antLLMChatModel == null) {
            throw new RuntimeException("MCP client or LLM model not available");
        }

        try {
            Prompt llmPrompt = new Prompt(prompt, AntLLMChatOptions.builder()
                    .withModel("Qwen3-235B-A22B")
                    .withTemperature(0.1) // 低温度确保结果稳定
                    .build());

            ChatResponse response = antLLMChatModel.call(llmPrompt);
            return response.getResult().getOutput().getText().trim();

        } catch (Exception e) {
            throw new RuntimeException("Failed to call LLM for intent analysis", e);
        }
    }

    /**
     * 解析并验证意图分析结果
     */
    private String parseAndValidateIntent(String intentResult, ExecutionContext context) {
        if (intentResult == null || intentResult.isEmpty()) {
            return "general_query";
        }

        // 清理结果，移除可能的标点符号和多余空格
        String cleanResult = intentResult.toLowerCase()
                .replaceAll("[\\[\\]\"'`]", "")
                .trim();

        // 定义有效的意图类别
        String[] validIntents = {
            "start_new_flow", "continue_flow", "check_status",
            "retry_step", "wait_check", "clear_restart",
            "help_info", "general_query"
        };

        // 检查是否包含步骤号的重试意图
        if (cleanResult.startsWith("retry_step:")) {
            String[] parts = cleanResult.split(":");
            if (parts.length == 2) {
                try {
                    int stepNumber = Integer.parseInt(parts[1]);
                    if (stepNumber >= 1 && stepNumber <= 6) {
                        return cleanResult; // 返回 "retry_step:3" 格式
                    }
                } catch (NumberFormatException e) {
                    // 忽略解析错误，继续处理
                }
            }
            return "retry_step"; // 如果步骤号无效，返回基本重试意图
        }

        // 验证是否为有效意图
        for (String validIntent : validIntents) {
            if (cleanResult.equals(validIntent)) {
                return validIntent;
            }
        }

        // 如果结果无效，尝试模糊匹配
        if (cleanResult.contains("start") || cleanResult.contains("new")) {
            return context.getCurrentStep() == 0 ? "start_new_flow" : "continue_flow";
        } else if (cleanResult.contains("status") || cleanResult.contains("check")) {
            return context.isHasWaitingInjections() ? "wait_check" : "check_status";
        } else if (cleanResult.contains("retry") || cleanResult.contains("again")) {
            return "retry_step";
        } else if (cleanResult.contains("clear") || cleanResult.contains("restart")) {
            return "clear_restart";
        } else if (cleanResult.contains("help") || cleanResult.contains("info")) {
            return "help_info";
        }

        return "general_query";
    }

    /**
     * 回退的基于规则的意图分析（当大模型不可用时）
     */
    private String fallbackIntentAnalysis(String userInput, ExecutionContext context) {
        String input = userInput.toLowerCase();

        // 开始/执行相关
        if (input.contains("开始") || input.contains("执行") || input.contains("start") ||
            input.contains("begin") || input.contains("run") || input.contains("启动")) {
            return context.getCurrentStep() == 0 ? "start_new_flow" : "continue_flow";
        }

        // 状态/进度查询
        if (input.contains("状态") || input.contains("进度") || input.contains("status") ||
            input.contains("progress") || input.contains("情况") || input.contains("如何")) {
            return "check_status";
        }

        // 重试相关
        if (input.contains("重试") || input.contains("重新") || input.contains("retry") ||
            input.contains("again") || input.contains("重做") || input.contains("再次")) {
            return "retry_step";
        }

        // 等待/检查相关
        if (input.contains("等待") || input.contains("检查") || input.contains("wait") ||
            input.contains("check") || input.contains("查看") || input.contains("结果")) {
            return context.isHasWaitingInjections() ? "wait_check" : "check_status";
        }

        // 清除/重启相关
        if (input.contains("清除") || input.contains("重启") || input.contains("clear") ||
            input.contains("restart") || input.contains("reset") || input.contains("重新开始")) {
            return "clear_restart";
        }

        // 帮助相关
        if (input.contains("帮助") || input.contains("help") || input.contains("怎么") ||
            input.contains("如何") || input.contains("说明") || input.contains("指导")) {
            return "help_info";
        }

        return "general_query";
    }

    /**
     * 从用户输入中提取步骤号
     */
    private int extractStepNumber(String userInput) {
        String input = userInput.toLowerCase();
        if (input.contains("步骤1") || input.contains("step 1")) return 1;
        if (input.contains("步骤2") || input.contains("step 2")) return 2;
        if (input.contains("步骤3") || input.contains("step 3")) return 3;
        if (input.contains("步骤4") || input.contains("step 4")) return 4;
        if (input.contains("步骤5") || input.contains("step 5")) return 5;
        if (input.contains("步骤6") || input.contains("step 6")) return 6;
        return 0;
    }

    // ==================== 原有方法保留 ====================

    /**
     * 执行完整的BKlight Mock流程（原有方法，保持兼容性）
     */
    public String executeFullBklightMockFlow(String caseId, String caseName,
                                           String downStreamInjectType, String relatedMachineGroup) {

        String prompt = String.format(
            "Please execute the complete BKlight mock testing flow with the following parameters:\n" +
            "- caseId: %s\n" +
            "- caseName: %s\n" +
            "- downStreamInjectType: %s\n" +
            "- relatedMachineGroup: %s\n\n" +
            "Execute the following steps in order:\n" +
            "1. Use addNewPlan tool to create a new test plan rule\n" +
            "2. Use triggerEte tool to trigger ETE execution\n" +
            "3. Use queryNewPlanList tool to query the new plan list and get caseInstanceId\n" +
            "4. Use addDependencyRule tool to add dependency rule and activate it\n" +
            "5. Use queryInjectResultList tool to query injection results\n\n" +
            "Please provide detailed results for each step.",
            caseId, caseName, downStreamInjectType, relatedMachineGroup
        );

        return chatWithMcp(prompt);
    }

    /**
     * 执行单个步骤的BKlight操作（原有方法，保持兼容性）
     */
    public String executeSingleStep(String stepName, String parameters) {
        String prompt = String.format(
            "Please execute the %s step with parameters: %s\n" +
            "Use the appropriate tool and provide detailed results.",
            stepName, parameters
        );

        return chatWithMcp(prompt);
    }

    /**
     * 通过MCP协议与Agent进行交互
     *
     * @param input 输入提示
     * @return Agent响应
     */
    public String chatWithMcp(String input) {
        if (sofaMcpClientFactory == null || antLLMChatModel == null) {
            return "MCP client factory or AntLLM chat model is not available";
        }

        try {
            SofaMcpToolCallbackProvider sofaMcpToolCallbackProvider =
                    new SofaMcpToolCallbackProvider(sofaMcpClientFactory.listMcpClients());

            Prompt prompt = new Prompt(input, AntLLMChatOptions.builder()
                    .withModel("Qwen3-235B-A22B")
                    .withToolCallbacks(sofaMcpToolCallbackProvider.getToolCallbacks())
                    .build());

            ChatResponse response = antLLMChatModel.call(prompt);
            return response.getResult().getOutput().getText();
        } catch (Exception e) {
            return "Error executing MCP chat: " + e.getMessage();
        }
    }

    /**
     * 获取可用的工具列表
     *
     * @return 工具列表描述
     */
    public String getAvailableTools() {
        String prompt = "Please list all available tools and their descriptions.";
        return chatWithMcp(prompt);
    }

    /**
     * 执行自定义的工具链
     *
     * @param customPrompt 自定义提示
     * @return 执行结果
     */
    public String executeCustomToolChain(String customPrompt) {
        return chatWithMcp(customPrompt);
    }

    /**
     * 从意图字符串中提取步骤号
     */
    private int extractStepNumberFromIntent(String intent) {
        if (intent.contains(":")) {
            String[] parts = intent.split(":");
            if (parts.length == 2) {
                try {
                    return Integer.parseInt(parts[1]);
                } catch (NumberFormatException e) {
                    return 0;
                }
            }
        }
        return 0;
    }

    /**
     * 重置指定步骤及后续步骤的状态
     */
    private void resetStepsFromNumber(ExecutionContext context, int stepNumber) {
        switch (stepNumber) {
            case 1:
                context.setStep1Completed(false);
                context.setNewPlanRuleId(null);
                // 继续重置后续步骤
            case 2:
                context.setStep2Completed(false);
                // 继续重置后续步骤
            case 3:
                context.setStep3Completed(false);
                context.setCaseInstanceId(null);
                // 继续重置后续步骤
            case 4:
                context.setStep4Completed(false);
                context.setDependencyRuleId(null);
                // 继续重置后续步骤
            case 5:
            case 6:
                context.setStep5Completed(false);
                context.setHasWaitingInjections(false);
                context.setLastWaitPromptTime(0);
                break;
        }

        // 更新当前步骤为重试的步骤
        context.setCurrentStep(stepNumber - 1);
    }

}
