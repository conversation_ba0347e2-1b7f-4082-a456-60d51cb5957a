package com.alipay.findataquality.service.ai.agent;

import com.alipay.sofa.ai.antllm.AntLLMChatModel;
import com.alipay.sofa.ai.antllm.AntLLMChatOptions;
import com.alipay.sofa.ai.mcp.SofaMcpToolCallbackProvider;
import com.alipay.sofa.ai.mcp.client.SofaMcpClientFactory;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * BKlight Mock Agent Service
 * 提供串联多个tools的Agent能力，通过MCP协议进行工具调用
 */
@Service
public class BklightMockAgentService {

    @Autowired(required = false)
    private SofaMcpClientFactory sofaMcpClientFactory;

    @Autowired(required = false)
    private AntLLMChatModel antLLMChatModel;

    /**
     * 执行完整的BKlight Mock流程
     * 包括：添加测新规则 -> 触发ETE执行 -> 查询测新流量列表 -> 添加依赖规则 -> 查询注入结果
     * 
     * @param caseId 用例ID
     * @param caseName 用例名称
     * @param downStreamInjectType 异常类型
     * @param relatedMachineGroup 机器组
     * @return 执行结果
     */
    public String executeFullBklightMockFlow(String caseId, String caseName, 
                                           String downStreamInjectType, String relatedMachineGroup) {
        
        String prompt = String.format(
            "Please execute the complete BKlight mock testing flow with the following parameters:\n" +
            "- caseId: %s\n" +
            "- caseName: %s\n" +
            "- downStreamInjectType: %s\n" +
            "- relatedMachineGroup: %s\n\n" +
            "Execute the following steps in order:\n" +
            "1. Use addNewPlan tool to create a new test plan rule\n" +
            "2. Use triggerEte tool to trigger ETE execution\n" +
            "3. Use queryNewPlanList tool to query the new plan list and get caseInstanceId\n" +
            "4. Use addDependencyRule tool to add dependency rule and activate it\n" +
            "5. Use queryInjectResultList tool to query injection results\n\n" +
            "Please provide detailed results for each step.",
            caseId, caseName, downStreamInjectType, relatedMachineGroup
        );

        return chatWithMcp(prompt);
    }

    /**
     * 执行单个步骤的BKlight操作
     * 
     * @param stepName 步骤名称
     * @param parameters 参数
     * @return 执行结果
     */
    public String executeSingleStep(String stepName, String parameters) {
        String prompt = String.format(
            "Please execute the %s step with parameters: %s\n" +
            "Use the appropriate tool and provide detailed results.",
            stepName, parameters
        );

        return chatWithMcp(prompt);
    }

    /**
     * 通过MCP协议与Agent进行交互
     * 
     * @param input 输入提示
     * @return Agent响应
     */
    public String chatWithMcp(String input) {
        if (sofaMcpClientFactory == null || antLLMChatModel == null) {
            return "MCP client factory or AntLLM chat model is not available";
        }

        try {
            SofaMcpToolCallbackProvider sofaMcpToolCallbackProvider =
                    new SofaMcpToolCallbackProvider(sofaMcpClientFactory.listMcpClients());
            
            Prompt prompt = new Prompt(input, AntLLMChatOptions.builder()
                    .withModel("Qwen3-235B-A22B")
                    .withToolCallbacks(sofaMcpToolCallbackProvider.getToolCallbacks())
                    .build());
            
            ChatResponse response = antLLMChatModel.call(prompt);
            return response.getResult().getOutput().getText();
        } catch (Exception e) {
            return "Error executing MCP chat: " + e.getMessage();
        }
    }

    /**
     * 获取可用的工具列表
     * 
     * @return 工具列表描述
     */
    public String getAvailableTools() {
        String prompt = "Please list all available tools and their descriptions.";
        return chatWithMcp(prompt);
    }

    /**
     * 执行自定义的工具链
     * 
     * @param customPrompt 自定义提示
     * @return 执行结果
     */
    public String executeCustomToolChain(String customPrompt) {
        return chatWithMcp(customPrompt);
    }
}
