package com.alipay.findataquality.service.ai.function_call;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.model.QueryTaskResult;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.result.FundFlowCheckResult;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.result.FundFlowHomoCheckResult;

public interface FundFlowTestFunctionCalls {

    /**
     * name:fetchDataScene
     * description:Add a new test plan rule. Input: caseId (test case ID) and caseName (test case name). Output: newly created test plan rule ID (newPlanRuleId)
     * @return
     */
    QueryTaskResult fetchDataScene(String sceneCode,String intputValue,String env);

    FundFlowCheckResult checkFundFlow(String shareCode);

    FundFlowHomoCheckResult homologousSourceCheck(String shareCode);
}
