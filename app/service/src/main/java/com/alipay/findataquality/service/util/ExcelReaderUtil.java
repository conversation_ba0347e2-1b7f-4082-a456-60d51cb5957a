package com.alipay.findataquality.service.util;

import com.alipay.findataquality.service.dto.dependencyAnalyzer.DependencyAnalysisMockData;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Excel文件读取工具类
 */
public class ExcelReaderUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(ExcelReaderUtil.class);
    
    /**
     * 读取xlsx文件并生成DependencyAnalysisMockData列表
     * 
     * @param filePath xlsx文件路径
     * @return DependencyAnalysisMockData列表
     * @throws IOException 文件读取异常
     */
    public static List<DependencyAnalysisMockData> readXlsxFile(String filePath) throws IOException {
        try (FileInputStream fileInputStream = new FileInputStream(filePath)) {
            return readXlsxFromInputStream(fileInputStream);
        }
    }
    
    /**
     * 从InputStream读取xlsx文件并生成DependencyAnalysisMockData列表
     * 
     * @param inputStream 输入流
     * @return DependencyAnalysisMockData列表
     * @throws IOException 文件读取异常
     */
    public static List<DependencyAnalysisMockData> readXlsxFromInputStream(InputStream inputStream) throws IOException {
        List<DependencyAnalysisMockData> dataList = new ArrayList<>();
        
        try (Workbook workbook = new XSSFWorkbook(inputStream)) {
            Sheet sheet = workbook.getSheetAt(0); // 获取第一个Sheet
            
            // 跳过标题行，从第二行开始读取数据
            for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row == null) {
                    continue;
                }
                
                DependencyAnalysisMockData mockData = new DependencyAnalysisMockData();
                
                // 创建单元格映射
                Map<String, Cell> cellMap = new HashMap<>();
                for (int columnIndex = 0; columnIndex < row.getPhysicalNumberOfCells(); columnIndex++) {
                    Cell cell = row.getCell(columnIndex);
                    cellMap.put(String.valueOf(columnIndex), cell);
                }
                
                // 按照列索引设置数据
                mockData.setRuleId(getCellValueAsString(cellMap.get("0")));
                mockData.setCaseType(getCellValueAsString(cellMap.get("1")));
                mockData.setCaseId(getCellValueAsString(cellMap.get("2")));
                mockData.setStandardInstanceId(getCellValueAsString(cellMap.get("3")));
                mockData.setUpperNodeId(getCellValueAsString(cellMap.get("4")));
                mockData.setUpperNodeApp(getCellValueAsString(cellMap.get("5")));
                mockData.setUpperNodeName(getCellValueAsString(cellMap.get("6")));
                mockData.setDownNodeInjectType(getCellValueAsString(cellMap.get("7")));
                mockData.setGroupId(getCellValueAsString(cellMap.get("8")));
                mockData.setUpperNodeMonitorRule(getCellValueAsString(cellMap.get("9")));
                mockData.setDownNodeId(getCellValueAsString(cellMap.get("10")));
                mockData.setDownNodeServiceName(getCellValueAsString(cellMap.get("11")));
                mockData.setDownNodeAppName(getCellValueAsString(cellMap.get("12")));
                mockData.setDownInjectRule(getCellValueAsString(cellMap.get("13")));
                mockData.setDownInjectTypeV2(getCellValueAsString(cellMap.get("14")));
                mockData.setDownErrorRuleStatus(getCellValueAsString(cellMap.get("15")));
                mockData.setDownRuleHitInstanceId(getCellValueAsString(cellMap.get("16")));
                mockData.setDependencyNodeInitParam(getCellValueAsString(cellMap.get("17")));
                mockData.setDependencyNodeRuleInjectParam(getCellValueAsString(cellMap.get("18")));
                mockData.setDependencyNodeInitResponse(getCellValueAsString(cellMap.get("19")));
                mockData.setDependencyNodeRuleInjectResponse(getCellValueAsString(cellMap.get("20")));
                mockData.setDependencyNodeInitErrorInfo(getCellValueAsString(cellMap.get("21")));
                mockData.setDependencyNodeRuleInjectErrorInfo(getCellValueAsString(cellMap.get("22")));
                mockData.setInstanceExecContext(getCellValueAsString(cellMap.get("23")));
                mockData.setHitInstanceNodeResponse(getCellValueAsString(cellMap.get("24")));
                
                dataList.add(mockData);
                logger.debug("读取到数据行 {}: {}", rowIndex, mockData.getRuleId());
            }
        }
        
        logger.info("成功读取Excel文件，共 {} 条数据", dataList.size());
        return dataList;
    }
    
    /**
     * 将单元格数据转为字符串
     * 
     * @param cell 单元格
     * @return 字符串值
     */
    private static String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }
        
        DataFormatter dataFormatter = new DataFormatter();
        return switch (cell.getCellType()) {
            case STRING -> dataFormatter.formatCellValue(cell).trim();
            case NUMERIC -> {
                if (DateUtil.isCellDateFormatted(cell)) {
                    yield dataFormatter.formatCellValue(cell);
                } else {
                    // 处理数字类型，避免科学计数法
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == (long) numericValue) {
                        yield String.valueOf((long) numericValue);
                    } else {
                        yield String.valueOf(numericValue);
                    }
                }
            }
            case BOOLEAN -> String.valueOf(cell.getBooleanCellValue());
            case FORMULA -> {
                try {
                    yield dataFormatter.formatCellValue(cell);
                } catch (Exception e) {
                    logger.warn("公式计算失败: {}", e.getMessage());
                    yield cell.getCellFormula();
                }
            }
            case BLANK -> "";
            default -> "";
        };
    }
    
    /**
     * 验证Excel文件格式
     * 
     * @param filePath 文件路径
     * @return 是否为有效的xlsx文件
     */
    public static boolean isValidXlsxFile(String filePath) {
        if (filePath == null || !filePath.toLowerCase().endsWith(".xlsx")) {
            return false;
        }
        
        try (FileInputStream fileInputStream = new FileInputStream(filePath);
             Workbook workbook = new XSSFWorkbook(fileInputStream)) {
            return workbook.getNumberOfSheets() > 0;
        } catch (IOException e) {
            logger.error("验证Excel文件失败: {}", e.getMessage());
            return false;
        }
    }
}
