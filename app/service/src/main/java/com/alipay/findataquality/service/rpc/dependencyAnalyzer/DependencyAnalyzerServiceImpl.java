package com.alipay.findataquality.service.rpc.dependencyAnalyzer;

import com.alipay.findataquality.facade.rpc.dependencyAnalyzer.DependencyAnalyzerService;
import com.alipay.findataquality.facade.rpc.dependencyAnalyzer.model.BklightMockData;
import com.alipay.findataquality.facade.rpc.dependencyAnalyzer.model.DependencyAnalyzerModel;
import com.alipay.findataquality.facade.rpc.dependencyAnalyzer.result.DependencyAnalyzerResult;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.utils.StrUtil;
import com.alipay.findataquality.service.dto.dependencyAnalyzer.*;
import com.alipay.findataquality.service.util.BakeryToolsUtil;
import com.alipay.sofa.rpc.api.annotation.RpcProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

import static com.alipay.findataquality.service.constant.DependencyAnalyzerConstant.*;


@RpcProvider
public class DependencyAnalyzerServiceImpl implements DependencyAnalyzerService {

    private static final Logger logger = LoggerFactory.getLogger(DependencyAnalyzerService.class);

//    @Autowired
//    private JobFacade jobFacade;

    /**
     * 依赖分析服务入口：转出页改版示例-https://bakery.alipay.com/bakery/yuebao/yeb-trade/sprints/S090011007433/uiauto_scene
     * @param bklightMockDataList
     * @return
     */
    @Override
    public DependencyAnalyzerResult analysisDependency(List<BklightMockData> bklightMockDataList) {
        DependencyAnalyzerResult result = new DependencyAnalyzerResult();
        //处理返回值
        //todo:从配置db里面获取scene信息
        String sceneId = StrUtil.calculateMD5("YEB"+"com.alipay.yebtradebff.transferOut.prepare"+"operation=TRANSFER_OUT"+"S090011007433");
        String sceneName = "余额宝主动转出页";
        //组装对照组
        String comparisonData_response="";//todo：从db获取
        UIMockQueryDetailResult uiMockQueryDetailResult_comparision =  BakeryToolsUtil.addBakeryCase(new BklightMockData(),comparisonData_response,false);
        String bakeryUrl_comparison= uiMockQueryDetailResult_comparision.getUrl();
        String statisticPicUrl_comparison= uiMockQueryDetailResult_comparision.getThumbnail();//截图+获取静态图片
        logger.info("对照组：bakeryId：{},bakery演示地址bakeryUrl：{},静态页面图片地址statisticPicUrl:{}",uiMockQueryDetailResult_comparision.getId(),bakeryUrl_comparison,statisticPicUrl_comparison);
        DependencyAnalyzerModel dependencyAnalyzerModel_comparison = new DependencyAnalyzerModel();
        dependencyAnalyzerModel_comparison.setCaseId(uiMockQueryDetailResult_comparision.getId());//todo：待替换，当前为查询bakery的id
        dependencyAnalyzerModel_comparison.setCaseName(uiMockQueryDetailResult_comparision.getName());
        //sceneId=md5("YEB"+"com.alipay.yebtradebff.transferOut.prepare"+"operation=TRANSFER_OUT"+雨燕迭代地址)
        //雨燕迭代地址：“https://yuyan.antfin-inc.com/yuebao/yeb-trade/sprints/S090011007433/overview”取“S090011007433”
        dependencyAnalyzerModel_comparison.setSceneId(sceneId);
        dependencyAnalyzerModel_comparison.setSceneName(sceneName);
        dependencyAnalyzerModel_comparison.setAbnormalDetail("");//下游应用名称#下游节点名称#下游异常注入类型
        dependencyAnalyzerModel_comparison.setBakeryUrl(bakeryUrl_comparison);
        dependencyAnalyzerModel_comparison.setBakeryStaticPicUrl(statisticPicUrl_comparison);
        dependencyAnalyzerModel_comparison.setControlGroup(true);//是否是对照组，默认为实验组

        //组装实验组
        List<DependencyAnalyzerModel> treatmentDependencyAnalyzerModels = new LinkedList<>();
        for (BklightMockData bklightMockData : bklightMockDataList){
            UIMockQueryDetailResult uiMockQueryDetailResult = BakeryToolsUtil.addBakeryCase(bklightMockData,null,true);
            String bakeryUrl= uiMockQueryDetailResult.getUrl();
            String statisticPicUrl= uiMockQueryDetailResult.getThumbnail();//截图+获取静态图片
            logger.info("对照组：bakeryId：{},bakery演示地址bakeryUrl：{},静态页面图片地址statisticPicUrl:{}",uiMockQueryDetailResult.getId(),bakeryUrl,statisticPicUrl);
            DependencyAnalyzerModel dependencyAnalyzerModel = new DependencyAnalyzerModel();
            dependencyAnalyzerModel.setCaseId(uiMockQueryDetailResult.getId());//todo：待替换，当前为查询bakery的id
            dependencyAnalyzerModel.setCaseName(uiMockQueryDetailResult.getName());
            //sceneId=md5("YEB"+"com.alipay.yebtradebff.transferOut.prepare"+"operation=TRANSFER_OUT"+雨燕迭代地址)
            //雨燕迭代地址：“https://yuyan.antfin-inc.com/yuebao/yeb-trade/sprints/S090011007433/overview”取“S090011007433”
            dependencyAnalyzerModel.setSceneId(sceneId);
            dependencyAnalyzerModel.setSceneName(sceneName);
            dependencyAnalyzerModel.setAbnormalDetail(bklightMockData.getDown_node_app_name()+"#"+bklightMockData.getDown_node_service_name()+"#"+bklightMockData.getDown_node_inject_type());//下游应用名称#下游节点名称#下游异常注入类型
            dependencyAnalyzerModel.setBakeryUrl(bakeryUrl);
            dependencyAnalyzerModel.setBakeryStaticPicUrl(statisticPicUrl);
            dependencyAnalyzerModel.setControlGroup(false);//是否是对照组，默认为实验组

            //todo：自动判断强弱依赖--能力待搭建
            dependencyAnalyzerModel.setDependencyType(DEPENDENCY_JUDGE_TYPE_STRONG);
            dependencyAnalyzerModel.setJudgmentInfo("");
            dependencyAnalyzerModel.setValid(true);//todo:强弱依赖判断有效性，默认有效
            //
            treatmentDependencyAnalyzerModels.add(dependencyAnalyzerModel);
        }
        logger.info("dependencyAnalyzerModels: {}", com.alibaba.fastjson.JSON.toJSONString(treatmentDependencyAnalyzerModels));// 打印dependencyAnalyzerModels
        result.setSuccess(true);
        result.setSceneId(sceneId);
        result.setSceneName(sceneName);
        result.setIterateAddress("S090011007433");
        result.setComparisonGroup(dependencyAnalyzerModel_comparison);
        result.setTreatmentGroupList(treatmentDependencyAnalyzerModels);

        logger.info("解析并添加用例后返回结果: {}", com.alibaba.fastjson.JSON.toJSONString(result));

        return result;
    }


}
