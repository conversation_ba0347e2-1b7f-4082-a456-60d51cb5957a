package com.alipay.findataquality.service.ai.mcp;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.FetchFullTraceDBFacade;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.model.QueryTaskResult;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.request.FetchDataRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.beans.factory.annotation.Autowired;

public class MCPServerFundFlowTools {

    private static final Logger logger = LoggerFactory.getLogger(MCPServerFundFlowTools.class);


    @Tool(name="fetchDataScene", description = "Add a new test plan rule. Input: caseId (test case ID) and caseName (test case name). Output: newly created test plan rule ID (newPlanRuleId)")
    QueryTaskResult fetchDataScene(FetchDataRequest request) {

    }
}
