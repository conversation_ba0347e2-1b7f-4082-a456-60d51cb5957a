package com.alipay.findataquality.service.ai.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * BKlight Mock Agent 配置类
 * 统一管理大模型相关配置参数
 */
@Configuration
@ConfigurationProperties(prefix = "bklight.agent")
public class BklightAgentConfig {

    /**
     * 大模型配置
     */
    private LlmConfig llm = new LlmConfig();

    /**
     * 意图分析配置
     */
    private IntentAnalysisConfig intentAnalysis = new IntentAnalysisConfig();

    /**
     * 监控配置
     */
    private MonitoringConfig monitoring = new MonitoringConfig();

    /**
     * 日志配置
     */
    private LoggingConfig logging = new LoggingConfig();

    /**
     * 指标配置
     */
    private MetricsConfig metrics = new MetricsConfig();

    // Getters and Setters
    public LlmConfig getLlm() { return llm; }
    public void setLlm(LlmConfig llm) { this.llm = llm; }
    public IntentAnalysisConfig getIntentAnalysis() { return intentAnalysis; }
    public void setIntentAnalysis(IntentAnalysisConfig intentAnalysis) { this.intentAnalysis = intentAnalysis; }
    public MonitoringConfig getMonitoring() { return monitoring; }
    public void setMonitoring(MonitoringConfig monitoring) { this.monitoring = monitoring; }
    public LoggingConfig getLogging() { return logging; }
    public void setLogging(LoggingConfig logging) { this.logging = logging; }
    public MetricsConfig getMetrics() { return metrics; }
    public void setMetrics(MetricsConfig metrics) { this.metrics = metrics; }

    /**
     * 大模型配置类
     */
    public static class LlmConfig {
        /**
         * 模型名称
         */
        private String model = "Qwen3-235B-A22B";

        /**
         * 温度参数
         */
        private Float temperature = 0.1f;

        /**
         * 最大重试次数
         */
        private Integer maxRetry = 3;

        /**
         * 请求超时时间（秒）
         */
        private Integer timeout = 30;

        // Getters and Setters
        public String getModel() { return model; }
        public void setModel(String model) { this.model = model; }
        public Float getTemperature() { return temperature; }
        public void setTemperature(Float temperature) { this.temperature = temperature; }
        public Integer getMaxRetry() { return maxRetry; }
        public void setMaxRetry(Integer maxRetry) { this.maxRetry = maxRetry; }
        public Integer getTimeout() { return timeout; }
        public void setTimeout(Integer timeout) { this.timeout = timeout; }
    }

    /**
     * 意图分析配置类
     */
    public static class IntentAnalysisConfig {
        /**
         * 意图分析专用温度
         */
        private Float temperature = 0.1f;

        /**
         * 缓存配置
         */
        private CacheConfig cache = new CacheConfig();

        // Getters and Setters
        public Float getTemperature() { return temperature; }
        public void setTemperature(Float temperature) { this.temperature = temperature; }
        public CacheConfig getCache() { return cache; }
        public void setCache(CacheConfig cache) { this.cache = cache; }

        /**
         * 缓存配置类
         */
        public static class CacheConfig {
            /**
             * 是否启用缓存
             */
            private Boolean enabled = true;

            /**
             * 缓存过期时间（分钟）
             */
            private Integer expireMinutes = 60;

            // Getters and Setters
            public Boolean getEnabled() { return enabled; }
            public void setEnabled(Boolean enabled) { this.enabled = enabled; }
            public Integer getExpireMinutes() { return expireMinutes; }
            public void setExpireMinutes(Integer expireMinutes) { this.expireMinutes = expireMinutes; }
        }
    }

    /**
     * 监控配置类
     */
    public static class MonitoringConfig {
        /**
         * 是否启用监控
         */
        private Boolean enabled = true;

        // Getters and Setters
        public Boolean getEnabled() { return enabled; }
        public void setEnabled(Boolean enabled) { this.enabled = enabled; }
    }

    /**
     * 日志配置类
     */
    public static class LoggingConfig {
        /**
         * 是否记录大模型调用日志
         */
        private Boolean llmCalls = false;

        /**
         * 是否记录意图分析详情
         */
        private Boolean intentAnalysis = false;

        // Getters and Setters
        public Boolean getLlmCalls() { return llmCalls; }
        public void setLlmCalls(Boolean llmCalls) { this.llmCalls = llmCalls; }
        public Boolean getIntentAnalysis() { return intentAnalysis; }
        public void setIntentAnalysis(Boolean intentAnalysis) { this.intentAnalysis = intentAnalysis; }
    }

    /**
     * 指标配置类
     */
    public static class MetricsConfig {
        /**
         * 是否启用指标收集
         */
        private Boolean enabled = true;

        // Getters and Setters
        public Boolean getEnabled() { return enabled; }
        public void setEnabled(Boolean enabled) { this.enabled = enabled; }
    }

    /**
     * 获取当前配置摘要
     */
    public String getConfigSummary() {
        return String.format(
            "BKlight Agent Config: model=%s, temperature=%.2f, intentTemp=%.2f, monitoring=%s",
            llm.getModel(),
            llm.getTemperature(),
            intentAnalysis.getTemperature(),
            monitoring.getEnabled()
        );
    }

    /**
     * 验证配置有效性
     */
    public boolean isValid() {
        return llm.getModel() != null && !llm.getModel().trim().isEmpty() &&
               llm.getTemperature() != null && llm.getTemperature() >= 0.0f && llm.getTemperature() <= 2.0f &&
               intentAnalysis.getTemperature() != null && intentAnalysis.getTemperature() >= 0.0f && intentAnalysis.getTemperature() <= 2.0f;
    }

    /**
     * 获取推荐的生产环境配置
     */
    public static BklightAgentConfig getProductionConfig() {
        BklightAgentConfig config = new BklightAgentConfig();
        config.getLlm().setModel("Qwen3-235B-A22B");
        config.getLlm().setTemperature(0.1f);
        config.getIntentAnalysis().setTemperature(0.05f);
        config.getLlm().setTimeout(60);
        config.getLlm().setMaxRetry(5);
        return config;
    }

    /**
     * 获取推荐的开发环境配置
     */
    public static BklightAgentConfig getDevelopmentConfig() {
        BklightAgentConfig config = new BklightAgentConfig();
        config.getLlm().setModel("Qwen3-72B-A14B");
        config.getLlm().setTemperature(0.2f);
        config.getIntentAnalysis().setTemperature(0.1f);
        config.getLlm().setTimeout(30);
        config.getLlm().setMaxRetry(3);
        config.getLogging().setLlmCalls(true);
        config.getLogging().setIntentAnalysis(true);
        return config;
    }
}
