package com.alipay.findataquality.service.ai.agent;

import com.alipay.findataquality.service.ai.config.BklightAgentConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SmartChat 集成测试类
 * 测试真实的smartChat功能，包括与实际服务的集成
 */
@SpringBootTest(classes = BklightTestConfiguration.class)
@SpringJUnitConfig
@ActiveProfiles("test")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class BklightMockAgentIntegrationTest {

    @Autowired(required = false)
    private BklightMockAgentService bklightMockAgentService;

    @Autowired(required = false)
    private BklightAgentConfig agentConfig;

    private String testUserId;
    private String testCaseId;
    private String testCaseName;
    private String testDownStreamInjectType;
    private String testRelatedMachineGroup;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testUserId = "integration-test-user-" + System.currentTimeMillis();
        testCaseId = "test-case-" + System.currentTimeMillis();
        testCaseName = "集成测试用例-" + System.currentTimeMillis();
        testDownStreamInjectType = "PRC_TIME_OUT_EXCEPTION";
        testRelatedMachineGroup = "GROUP_TEST_" + System.currentTimeMillis();
    }

    /**
     * 测试smartChat服务可用性
     */
    @Test
    @Order(1)
    @DisplayName("测试smartChat服务可用性")
    public void testSmartChatServiceAvailability() {
        System.out.println("=== SmartChat Service Availability Test ===");
        
        // 检查服务是否注入成功
        if (bklightMockAgentService == null) {
            System.out.println("⚠️ BklightMockAgentService not available, skipping integration tests");
            return;
        }
        
        assertNotNull(bklightMockAgentService, "BklightMockAgentService should be available");
        
        // 测试基本的帮助功能
        try {
            String response = bklightMockAgentService.smartChatWithContext(testUserId, "帮助");
            assertNotNull(response, "Help response should not be null");
            assertFalse(response.trim().isEmpty(), "Help response should not be empty");
            
            System.out.println("✓ SmartChat service is available and responsive");
            System.out.println("Help response preview: " + response.substring(0, Math.min(100, response.length())) + "...");
        } catch (Exception e) {
            System.out.println("⚠️ SmartChat service error: " + e.getMessage());
            // 在集成测试中，我们可以容忍某些外部依赖不可用
        }
        
        System.out.println("=== Service Availability Test Completed ===");
    }

    /**
     * 测试smartChat状态查询功能
     */
    @Test
    @Order(2)
    @DisplayName("测试smartChat状态查询功能")
    public void testSmartChatStatusQuery() {
        System.out.println("=== SmartChat Status Query Test ===");
        
        if (bklightMockAgentService == null) {
            System.out.println("⚠️ Service not available, skipping test");
            return;
        }
        
        try {
            // 测试状态查询
            String response = bklightMockAgentService.smartChatWithContext(testUserId, "查看状态");
            assertNotNull(response);
            
            System.out.println("✓ Status query successful");
            System.out.println("Status response: " + response.substring(0, Math.min(200, response.length())) + "...");
            
            // 测试英文状态查询
            String englishResponse = bklightMockAgentService.smartChatWithContext(testUserId, "check status");
            assertNotNull(englishResponse);
            
            System.out.println("✓ English status query successful");
            
        } catch (Exception e) {
            System.out.println("⚠️ Status query error: " + e.getMessage());
        }
        
        System.out.println("=== Status Query Test Completed ===");
    }

    /**
     * 测试smartChat意图识别准确性
     */
    @Test
    @Order(3)
    @DisplayName("测试smartChat意图识别准确性")
    public void testSmartChatIntentAccuracy() {
        System.out.println("=== SmartChat Intent Accuracy Test ===");
        
        if (bklightMockAgentService == null) {
            System.out.println("⚠️ Service not available, skipping test");
            return;
        }
        
        // 测试各种意图表达
        String[][] intentTests = {
            {"开始执行测试", "start"},
            {"查看当前状态", "status"},
            {"帮助我", "help"},
            {"重新开始", "restart"},
            {"start testing", "start"},
            {"show me status", "status"},
            {"help", "help"}
        };
        
        int successCount = 0;
        
        for (String[] test : intentTests) {
            String input = test[0];
            String expectedKeyword = test[1];
            
            try {
                String response = bklightMockAgentService.smartChatWithContext(testUserId, input);
                assertNotNull(response);
                
                // 简单的关键词匹配验证
                boolean containsExpected = response.toLowerCase().contains(expectedKeyword) ||
                                         response.contains("执行") ||
                                         response.contains("状态") ||
                                         response.contains("帮助") ||
                                         response.contains("重新");
                
                if (containsExpected) {
                    successCount++;
                    System.out.println("✓ Intent test passed: \"" + input + "\"");
                } else {
                    System.out.println("⚠️ Intent test unclear: \"" + input + "\" -> " + response.substring(0, Math.min(50, response.length())));
                }
                
            } catch (Exception e) {
                System.out.println("⚠️ Intent test error for \"" + input + "\": " + e.getMessage());
            }
        }
        
        System.out.println("Intent accuracy: " + successCount + "/" + intentTests.length + " tests passed");
        System.out.println("=== Intent Accuracy Test Completed ===");
    }

    /**
     * 测试smartChat配置集成
     */
    @Test
    @Order(4)
    @DisplayName("测试smartChat配置集成")
    public void testSmartChatConfigurationIntegration() {
        System.out.println("=== SmartChat Configuration Integration Test ===");
        
        if (agentConfig == null) {
            System.out.println("⚠️ AgentConfig not available, skipping configuration test");
            return;
        }
        
        // 验证配置是否正确加载
        assertNotNull(agentConfig.getLlm(), "LLM config should be loaded");
        assertNotNull(agentConfig.getLlm().getModel(), "LLM model should be configured");
        assertNotNull(agentConfig.getLlm().getTemperature(), "LLM temperature should be configured");
        
        System.out.println("✓ Configuration loaded successfully:");
        System.out.println("  - Model: " + agentConfig.getLlm().getModel());
        System.out.println("  - Temperature: " + agentConfig.getLlm().getTemperature());
        System.out.println("  - Intent Analysis Temperature: " + agentConfig.getIntentAnalysis().getTemperature());
        
        // 验证配置有效性
        assertTrue(agentConfig.isValid(), "Configuration should be valid");
        
        System.out.println("✓ Configuration validation passed");
        System.out.println("=== Configuration Integration Test Completed ===");
    }

    /**
     * 测试smartChat错误恢复能力
     */
    @Test
    @Order(5)
    @DisplayName("测试smartChat错误恢复能力")
    public void testSmartChatErrorRecovery() {
        System.out.println("=== SmartChat Error Recovery Test ===");
        
        if (bklightMockAgentService == null) {
            System.out.println("⚠️ Service not available, skipping test");
            return;
        }
        
        try {
            // 测试无效输入的处理
            String response1 = bklightMockAgentService.smartChatWithContext(testUserId, "无效的命令xyz123");
            assertNotNull(response1);
            System.out.println("✓ Invalid command handled gracefully");
            
            // 测试空输入的处理
            String response2 = bklightMockAgentService.smartChatWithContext(testUserId, "");
            assertNotNull(response2);
            System.out.println("✓ Empty input handled gracefully");
            
            // 测试特殊字符输入
            String response3 = bklightMockAgentService.smartChatWithContext(testUserId, "!@#$%^&*()");
            assertNotNull(response3);
            System.out.println("✓ Special characters handled gracefully");
            
            // 测试超长输入
            String longInput = "这是一个非常长的输入".repeat(50);
            String response4 = bklightMockAgentService.smartChatWithContext(testUserId, longInput);
            assertNotNull(response4);
            System.out.println("✓ Long input handled gracefully");
            
        } catch (Exception e) {
            System.out.println("⚠️ Error recovery test encountered exception: " + e.getMessage());
            // 在集成测试中，我们记录错误但不让测试失败
        }
        
        System.out.println("=== Error Recovery Test Completed ===");
    }

    /**
     * 测试smartChat性能基准
     */
    @Test
    @Order(6)
    @DisplayName("测试smartChat性能基准")
    public void testSmartChatPerformanceBenchmark() {
        System.out.println("=== SmartChat Performance Benchmark Test ===");
        
        if (bklightMockAgentService == null) {
            System.out.println("⚠️ Service not available, skipping test");
            return;
        }
        
        int testCount = 5;
        long totalTime = 0;
        int successCount = 0;
        
        for (int i = 0; i < testCount; i++) {
            try {
                long startTime = System.currentTimeMillis();
                String response = bklightMockAgentService.smartChatWithContext(testUserId, "测试性能 " + i);
                long endTime = System.currentTimeMillis();
                
                long responseTime = endTime - startTime;
                totalTime += responseTime;
                
                if (response != null && !response.trim().isEmpty()) {
                    successCount++;
                }
                
                System.out.println("Test " + (i + 1) + ": " + responseTime + "ms");
                
            } catch (Exception e) {
                System.out.println("Test " + (i + 1) + " failed: " + e.getMessage());
            }
        }
        
        if (successCount > 0) {
            long averageTime = totalTime / successCount;
            System.out.println("✓ Performance benchmark completed:");
            System.out.println("  - Success rate: " + successCount + "/" + testCount);
            System.out.println("  - Average response time: " + averageTime + "ms");
            
            // 性能断言（可以根据实际需求调整）
            assertTrue(averageTime < 10000, "Average response time should be less than 10 seconds");
        }
        
        System.out.println("=== Performance Benchmark Test Completed ===");
    }

    /**
     * 清理测试数据
     */
    @Test
    @Order(7)
    @DisplayName("清理测试数据")
    public void cleanupTestData() {
        System.out.println("=== Cleanup Test Data ===");
        
        if (bklightMockAgentService == null) {
            System.out.println("⚠️ Service not available, skipping cleanup");
            return;
        }
        
        try {
            // 清除测试用户的上下文
            bklightMockAgentService.clearUserContext(testUserId);
            System.out.println("✓ Test user context cleared: " + testUserId);
            
        } catch (Exception e) {
            System.out.println("⚠️ Cleanup error: " + e.getMessage());
        }
        
        System.out.println("=== Cleanup Completed ===");
    }
}
