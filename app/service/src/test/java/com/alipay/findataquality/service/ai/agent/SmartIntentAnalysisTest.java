package com.alipay.findataquality.service.ai.agent;

import org.junit.jupiter.api.Test;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

/**
 * 智能意图分析测试类
 * 测试大模型驱动的用户意图识别功能
 */

@SpringJUnitConfig
public class SmartIntentAnalysisTest {

    /**
     * 测试各种用户输入的意图识别
     */
    @Test
    public void testIntentAnalysisVariations() {
        System.out.println("=== Smart Intent Analysis Test ===");
        
        // 测试开始执行的各种表达
        String[] startInputs = {
            "开始执行BKlight Mock测试",
            "启动异常注入测试",
            "开始新的测试流程",
            "start testing",
            "begin the mock test",
            "run bklight test",
            "执行测试",
            "开始吧",
            "启动",
            "我想开始测试"
        };
        
        System.out.println("Testing START_NEW_FLOW intent variations:");
        for (String input : startInputs) {
            System.out.println("Input: \"" + input + "\" -> Expected: start_new_flow");
        }
        
        // 测试状态查询的各种表达
        String[] statusInputs = {
            "查看当前执行状态",
            "现在进行到哪一步了？",
            "当前进度如何？",
            "show me the status",
            "what's the current progress",
            "检查执行情况",
            "看看现在的状态",
            "进度怎么样",
            "执行到哪里了",
            "目前情况如何"
        };
        
        System.out.println("\nTesting CHECK_STATUS intent variations:");
        for (String input : statusInputs) {
            System.out.println("Input: \"" + input + "\" -> Expected: check_status");
        }
        
        // 测试重试的各种表达
        String[] retryInputs = {
            "重试步骤3",
            "重新执行第二步",
            "retry step 4",
            "再次运行步骤1",
            "重做第五步",
            "步骤2失败了，重试一下",
            "重新来一遍步骤3",
            "again step 2",
            "redo step 5",
            "重新执行依赖配置"
        };
        
        System.out.println("\nTesting RETRY_STEP intent variations:");
        for (String input : retryInputs) {
            System.out.println("Input: \"" + input + "\" -> Expected: retry_step or retry_step:N");
        }
        
        // 测试等待检查的各种表达
        String[] waitCheckInputs = {
            "等待30分钟后检查注入结果",
            "现在可以查看注入状态了吗？",
            "检查异常注入是否完成",
            "查看最新的注入结果",
            "wait check injection results",
            "check if injection is done",
            "注入完成了吗？",
            "看看注入情况",
            "检查WAIT状态",
            "查询注入执行结果"
        };
        
        System.out.println("\nTesting WAIT_CHECK intent variations:");
        for (String input : waitCheckInputs) {
            System.out.println("Input: \"" + input + "\" -> Expected: wait_check");
        }
        
        // 测试清除重启的各种表达
        String[] clearInputs = {
            "清除当前进度，重新开始",
            "重置所有状态",
            "clear context and restart",
            "reset everything",
            "重新来过",
            "清空上下文",
            "restart from beginning",
            "清除记录",
            "重新开始测试",
            "全部重置"
        };
        
        System.out.println("\nTesting CLEAR_RESTART intent variations:");
        for (String input : clearInputs) {
            System.out.println("Input: \"" + input + "\" -> Expected: clear_restart");
        }
        
        // 测试帮助信息的各种表达
        String[] helpInputs = {
            "帮助",
            "怎么使用这个系统？",
            "help me",
            "what can I do?",
            "使用说明",
            "操作指南",
            "如何进行测试？",
            "系统功能介绍",
            "我该怎么操作？",
            "有什么功能？"
        };
        
        System.out.println("\nTesting HELP_INFO intent variations:");
        for (String input : helpInputs) {
            System.out.println("Input: \"" + input + "\" -> Expected: help_info");
        }
        
        System.out.println("\n=== Smart Intent Analysis Test Completed ===");
    }

    /**
     * 测试复杂和模糊的用户输入
     */
    @Test
    public void testComplexAndAmbiguousInputs() {
        System.out.println("=== Complex and Ambiguous Input Test ===");
        
        String[] complexInputs = {
            "我想看看步骤3是否执行成功了，如果失败了就重试",
            "能帮我检查一下当前的注入状态吗？如果还在等待就算了",
            "刚才的测试好像有问题，我想重新开始整个流程",
            "The injection seems to be stuck, can you check the status and retry if needed?",
            "我等了半小时了，现在应该可以查看结果了吧？",
            "系统提示我等待10-30分钟，现在时间到了，查看结果",
            "步骤4执行失败了，错误信息说依赖规则创建失败，怎么办？",
            "I started the test 2 hours ago, but I'm not sure what happened, can you show me the current status?",
            "测试进行到一半断网了，现在网络恢复了，从哪里继续？",
            "Can you help me understand what went wrong and how to fix it?"
        };
        
        System.out.println("Testing complex and ambiguous inputs:");
        for (String input : complexInputs) {
            System.out.println("Input: \"" + input + "\"");
            System.out.println("Expected: LLM should analyze context and determine appropriate intent");
            System.out.println("---");
        }
        
        System.out.println("\n=== Complex Input Test Completed ===");
    }

    /**
     * 测试上下文相关的意图分析
     */
    @Test
    public void testContextAwareIntentAnalysis() {
        System.out.println("=== Context-Aware Intent Analysis Test ===");
        
        System.out.println("Scenario 1: User with no previous context");
        System.out.println("Input: '开始' -> Expected: start_new_flow");
        
        System.out.println("\nScenario 2: User with completed steps 1-4, has waiting injections");
        System.out.println("Input: '检查' -> Expected: wait_check");
        
        System.out.println("\nScenario 3: User with failed step 2");
        System.out.println("Input: '继续' -> Expected: continue_flow (should retry from step 2)");
        
        System.out.println("\nScenario 4: User with all steps completed");
        System.out.println("Input: '状态' -> Expected: check_status");
        
        System.out.println("\nScenario 5: User mentions specific step number");
        System.out.println("Input: '重试步骤3' -> Expected: retry_step:3");
        
        System.out.println("\n=== Context-Aware Test Completed ===");
    }

    /**
     * 测试多语言支持
     */
    @Test
    public void testMultiLanguageSupport() {
        System.out.println("=== Multi-Language Support Test ===");
        
        String[][] multiLangInputs = {
            {"中文", "开始执行测试", "start_new_flow"},
            {"English", "start the test", "start_new_flow"},
            {"中文", "查看状态", "check_status"},
            {"English", "check status", "check_status"},
            {"中文", "重试步骤2", "retry_step:2"},
            {"English", "retry step 2", "retry_step:2"},
            {"中文", "等待检查", "wait_check"},
            {"English", "wait and check", "wait_check"},
            {"中文", "清除重启", "clear_restart"},
            {"English", "clear and restart", "clear_restart"},
            {"中文", "帮助", "help_info"},
            {"English", "help", "help_info"}
        };
        
        System.out.println("Testing multi-language intent recognition:");
        for (String[] testCase : multiLangInputs) {
            System.out.println(String.format("Language: %s, Input: \"%s\" -> Expected: %s", 
                testCase[0], testCase[1], testCase[2]));
        }
        
        System.out.println("\n=== Multi-Language Test Completed ===");
    }
}
