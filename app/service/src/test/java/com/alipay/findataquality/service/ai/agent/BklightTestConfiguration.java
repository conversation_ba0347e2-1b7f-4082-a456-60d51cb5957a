//package com.alipay.findataquality.service.ai.agent;
//
//import com.alipay.findataquality.service.ai.config.BklightAgentConfig;
//import org.springframework.boot.autoconfigure.SpringBootApplication;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Primary;
//
///**
// * BKlight测试配置类
// * 为单元测试提供必要的Bean配置
// */
//@Configuration
//@SpringBootApplication(scanBasePackages = {"com.alipay.findataquality.service.ai"})
//public class BklightTestConfiguration {
//
//    /**
//     * 提供测试用的BklightAgentConfig
//     */
//    @Bean
//    @Primary
//    public BklightAgentConfig testBklightAgentConfig() {
//        BklightAgentConfig config = new BklightAgentConfig();
//
//        // 配置LLM设置
//        config.getLlm().setModel("Qwen3-72B-A14B"); // 测试环境使用较快的模型
//        config.getLlm().setTemperature(0.2);
//        config.getLlm().setMaxRetry(2);
//        config.getLlm().setTimeout(15);
//
//        // 配置意图分析设置
//        config.getIntentAnalysis().setTemperature(0.1);
//        config.getIntentAnalysis().getCache().setEnabled(true);
//        config.getIntentAnalysis().getCache().setExpireMinutes(5);
//
//        // 配置监控设置
//        config.getMonitoring().setEnabled(true);
//
//        // 配置日志设置
//        config.getLogging().setLlmCalls(true);
//        config.getLogging().setIntentAnalysis(true);
//
//        // 配置指标设置
//        config.getMetrics().setEnabled(true);
//
//        return config;
//    }
//
//    /**
//     * 提供测试用的BklightMockAgentService Mock
//     */
//    @Bean
//    @Primary
//    public BklightMockAgentService testBklightMockAgentService() {
//        // 这里可以返回一个Mock对象或者简化的实现
//        // 在实际测试中，这个Bean会被@MockBean覆盖
//        return new BklightMockAgentService() {
//            @Override
//            public String smartChatWithContext(String userId, String userInput) {
//                return "Test response for userId: " + userId + ", input: " + userInput;
//            }
//
//            // 可以添加其他必要的方法实现
//        };
//    }
//}
