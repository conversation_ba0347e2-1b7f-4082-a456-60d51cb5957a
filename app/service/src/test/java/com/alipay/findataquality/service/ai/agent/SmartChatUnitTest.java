package com.alipay.findataquality.service.ai.agent;

import com.alipay.findataquality.service.ai.config.BklightAgentConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * SmartChat 功能单元测试
 * 专门测试 smartChatWithContext 方法的各种场景
 */
@ExtendWith(MockitoExtension.class)
public class SmartChatUnitTest {

    @Mock
    private BklightMockAgentService bklightMockAgentService;

    @Mock
    private BklightAgentConfig agentConfig;

    private String testUserId;
    private String testCaseId;
    private String testCaseName;
    private String testDownStreamInjectType;
    private String testRelatedMachineGroup;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testUserId = "test-user-123";
        testCaseId = "a9cd8ba6-f714-41ab-bad0-4022a2f60126";
        testCaseName = "[余额宝][RPC]余额宝转出页渲染-新架构&新离线包[过yebtradebff]-黑卡+高客用户-提现页面渲染";
        testDownStreamInjectType = "PRC_TIME_OUT_EXCEPTION";
        testRelatedMachineGroup = "GROUP_20250630104315";
    }

    /**
     * 测试smartChat基本功能
     */
    @Test
    @DisplayName("测试smartChat基本功能")
    public void testSmartChatBasicFunctionality() {
        System.out.println("=== SmartChat Basic Functionality Test ===");
        
        // 模拟服务返回
        when(bklightMockAgentService.smartChatWithContext(anyString(), anyString()))
            .thenReturn("Mock response from smartChat");
        
        // 测试基本调用
        String response = bklightMockAgentService.smartChatWithContext(testUserId, "开始执行测试");
        
        // 验证结果
        assertNotNull(response);
        assertEquals("Mock response from smartChat", response);
        
        // 验证方法被调用
        verify(bklightMockAgentService, times(1))
            .smartChatWithContext(testUserId, "开始执行测试");
        
        System.out.println("✓ Basic functionality test passed");
        System.out.println("=== Test Completed ===");
    }

    /**
     * 测试smartChat意图识别功能
     */
    @Test
    @DisplayName("测试smartChat意图识别功能")
    public void testSmartChatIntentRecognition() {
        System.out.println("=== SmartChat Intent Recognition Test ===");
        
        // 测试不同意图的输入
        String[] testInputs = {
            "开始执行BKlight Mock测试",
            "查看当前执行状态", 
            "重试步骤3",
            "等待30分钟后检查注入结果",
            "清除当前进度，重新开始",
            "帮助",
            "start testing",
            "check status",
            "retry step 2"
        };
        
        String[] expectedIntents = {
            "start_new_flow",
            "check_status",
            "retry_step:3", 
            "wait_check",
            "clear_restart",
            "help_info",
            "start_new_flow",
            "check_status",
            "retry_step:2"
        };
        
        // 模拟不同意图的响应
        for (int i = 0; i < testInputs.length; i++) {
            String input = testInputs[i];
            String expectedIntent = expectedIntents[i];
            
            when(bklightMockAgentService.smartChatWithContext(testUserId, input))
                .thenReturn("Intent recognized: " + expectedIntent);
            
            String response = bklightMockAgentService.smartChatWithContext(testUserId, input);
            
            assertNotNull(response);
            assertTrue(response.contains(expectedIntent));
            
            System.out.println("✓ Input: \"" + input + "\" -> Intent: " + expectedIntent);
        }
        
        System.out.println("=== Intent Recognition Test Completed ===");
    }

    /**
     * 测试smartChat参数验证功能
     */
    @Test
    @DisplayName("测试smartChat参数验证功能")
    public void testSmartChatParameterValidation() {
        System.out.println("=== SmartChat Parameter Validation Test ===");
        
        // 测试空参数
        when(bklightMockAgentService.smartChatWithContext(isNull(), anyString()))
            .thenReturn("❌ 错误：缺少必填参数 userId");
        
        when(bklightMockAgentService.smartChatWithContext(anyString(), isNull()))
            .thenReturn("❌ 错误：缺少用户输入");
        
        when(bklightMockAgentService.smartChatWithContext(anyString(), eq("")))
            .thenReturn("❌ 错误：缺少用户输入");
        
        // 测试空userId
        String response1 = bklightMockAgentService.smartChatWithContext(null, "test");
        assertNotNull(response1);
        assertTrue(response1.contains("缺少必填参数 userId"));
        
        // 测试空userInput
        String response2 = bklightMockAgentService.smartChatWithContext("user123", null);
        assertNotNull(response2);
        assertTrue(response2.contains("缺少用户输入"));
        
        // 测试空字符串userInput
        String response3 = bklightMockAgentService.smartChatWithContext("user123", "");
        assertNotNull(response3);
        assertTrue(response3.contains("缺少用户输入"));
        
        System.out.println("✓ Parameter validation tests passed");
        System.out.println("=== Parameter Validation Test Completed ===");
    }

    /**
     * 测试smartChat上下文管理功能
     */
    @Test
    @DisplayName("测试smartChat上下文管理功能")
    public void testSmartChatContextManagement() {
        System.out.println("=== SmartChat Context Management Test ===");
        
        // 模拟首次使用场景
        when(bklightMockAgentService.smartChatWithContext(testUserId, "开始执行测试"))
            .thenReturn("检测到首次使用，请提供必填参数：caseId, caseName, downStreamInjectType, relatedMachineGroup");
        
        // 模拟有上下文的场景
        when(bklightMockAgentService.smartChatWithContext(testUserId, "查看状态"))
            .thenReturn("当前执行状态：步骤1已完成，步骤2进行中");
        
        // 模拟清除上下文场景
        when(bklightMockAgentService.smartChatWithContext(testUserId, "重新开始"))
            .thenReturn("上下文已清除，可以重新开始新的测试流程");
        
        // 测试首次使用
        String response1 = bklightMockAgentService.smartChatWithContext(testUserId, "开始执行测试");
        assertNotNull(response1);
        assertTrue(response1.contains("首次使用"));
        
        // 测试状态查询
        String response2 = bklightMockAgentService.smartChatWithContext(testUserId, "查看状态");
        assertNotNull(response2);
        assertTrue(response2.contains("当前执行状态"));
        
        // 测试清除上下文
        String response3 = bklightMockAgentService.smartChatWithContext(testUserId, "重新开始");
        assertNotNull(response3);
        assertTrue(response3.contains("上下文已清除"));
        
        System.out.println("✓ Context management tests passed");
        System.out.println("=== Context Management Test Completed ===");
    }

    /**
     * 测试smartChat多语言支持功能
     */
    @Test
    @DisplayName("测试smartChat多语言支持功能")
    public void testSmartChatMultiLanguageSupport() {
        System.out.println("=== SmartChat Multi-Language Support Test ===");
        
        // 中英文混合输入测试
        String[] multiLangInputs = {
            "开始执行测试",
            "start testing", 
            "查看状态",
            "check status",
            "重试步骤3",
            "retry step 2",
            "请help我start这个testing",
            "能否check一下current status？"
        };
        
        // 模拟多语言响应
        for (String input : multiLangInputs) {
            when(bklightMockAgentService.smartChatWithContext(testUserId, input))
                .thenReturn("Successfully processed multi-language input: " + input);
            
            String response = bklightMockAgentService.smartChatWithContext(testUserId, input);
            
            assertNotNull(response);
            assertTrue(response.contains("Successfully processed"));
            
            System.out.println("✓ Multi-language input: \"" + input + "\" processed successfully");
        }
        
        System.out.println("=== Multi-Language Support Test Completed ===");
    }

    /**
     * 测试smartChat错误处理功能
     */
    @Test
    @DisplayName("测试smartChat错误处理功能")
    public void testSmartChatErrorHandling() {
        System.out.println("=== SmartChat Error Handling Test ===");
        
        // 模拟各种错误场景
        when(bklightMockAgentService.smartChatWithContext(testUserId, "invalid_command"))
            .thenReturn("未能识别您的意图，请提供更清晰的指令");
        
        when(bklightMockAgentService.smartChatWithContext(eq(""), anyString()))
            .thenReturn("❌ 错误：用户ID不能为空");
        
        // 模拟系统异常
        when(bklightMockAgentService.smartChatWithContext(testUserId, "system_error"))
            .thenReturn("系统暂时不可用，请稍后重试");
        
        // 测试无效命令
        String response1 = bklightMockAgentService.smartChatWithContext(testUserId, "invalid_command");
        assertNotNull(response1);
        assertTrue(response1.contains("未能识别"));
        
        // 测试空用户ID
        String response2 = bklightMockAgentService.smartChatWithContext("", "test");
        assertNotNull(response2);
        assertTrue(response2.contains("用户ID不能为空"));
        
        // 测试系统异常
        String response3 = bklightMockAgentService.smartChatWithContext(testUserId, "system_error");
        assertNotNull(response3);
        assertTrue(response3.contains("系统暂时不可用"));
        
        System.out.println("✓ Error handling tests passed");
        System.out.println("=== Error Handling Test Completed ===");
    }

    /**
     * 测试smartChat完整工作流程
     */
    @Test
    @DisplayName("测试smartChat完整工作流程")
    public void testSmartChatCompleteWorkflow() {
        System.out.println("=== SmartChat Complete Workflow Test ===");
        
        // 模拟完整的工作流程
        String[] workflowSteps = {
            "开始执行BKlight Mock测试",
            "查看当前执行状态", 
            "重试步骤2",
            "检查注入结果",
            "查看最终状态"
        };
        
        String[] expectedResponses = {
            "开始执行完整的BKlight Mock测试流程...",
            "当前执行状态：步骤1已完成，步骤2进行中",
            "正在重试步骤2：触发ETE执行",
            "正在检查异常注入执行结果...",
            "测试流程已完成，所有步骤执行成功"
        };
        
        // 模拟完整工作流程
        for (int i = 0; i < workflowSteps.length; i++) {
            String step = workflowSteps[i];
            String expectedResponse = expectedResponses[i];
            
            when(bklightMockAgentService.smartChatWithContext(testUserId, step))
                .thenReturn(expectedResponse);
            
            String response = bklightMockAgentService.smartChatWithContext(testUserId, step);
            
            assertNotNull(response);
            assertEquals(expectedResponse, response);
            
            System.out.println("✓ Step " + (i + 1) + ": " + step);
            System.out.println("  Response: " + response.substring(0, Math.min(50, response.length())) + "...");
        }
        
        // 验证所有步骤都被调用
        verify(bklightMockAgentService, times(workflowSteps.length))
            .smartChatWithContext(eq(testUserId), anyString());
        
        System.out.println("✓ Complete workflow test passed");
        System.out.println("=== Complete Workflow Test Completed ===");
    }
}
