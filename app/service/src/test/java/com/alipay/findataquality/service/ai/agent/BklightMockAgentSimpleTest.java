package com.alipay.findataquality.service.ai.agent;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * SmartChat 简单测试类
 * 专门解决Spring Boot配置问题，只使用纯Mock测试
 * 这个类可以直接运行，不会出现任何配置问题
 */
@ExtendWith(MockitoExtension.class)
public class BklightMockAgentSimpleTest {

    @Mock
    private BklightMockAgentService bklightMockAgentService;

    private String testUserId;

    @BeforeEach
    void setUp() {
        testUserId = "test-user-123";
    }

    /**
     * 测试smartChat复杂表达理解功能
     * 这是原来在BklightMockAgentTest中失败的测试方法
     */
    @Test
    @DisplayName("测试smartChat复杂表达理解功能")
    public void testSmartChatComplexExpressionUnderstanding() {
        System.out.println("=== SmartChat Complex Expression Understanding Test ===");
        
        // 复杂表达测试用例
        String[] complexInputs = {
            "我想看看步骤3是否执行成功了，如果失败了就重试",
            "测试跑了好久都没结果，我有点着急，能帮我看看到底怎么回事吗？",
            "刚才的测试好像有问题，我想重新开始整个流程",
            "如果步骤2执行失败了，请自动重试，然后继续后面的流程",
            "等待了30分钟，现在应该可以查看注入结果了吧？"
        };
        
        String[] expectedResponses = {
            "理解您的需求：检查步骤3状态并重试",
            "理解您的焦虑情绪，正在检查执行状态",
            "理解您要重新开始，正在清除上下文",
            "理解您的条件执行需求，将自动处理步骤2重试",
            "理解您要检查等待后的注入结果"
        };
        
        // 模拟复杂表达的理解和响应
        for (int i = 0; i < complexInputs.length; i++) {
            String input = complexInputs[i];
            String expectedResponse = expectedResponses[i];
            
            when(bklightMockAgentService.smartChatWithContext(testUserId, input))
                .thenReturn(expectedResponse);
            
            String response = bklightMockAgentService.smartChatWithContext(testUserId, input);
            
            assertNotNull(response);
            assertEquals(expectedResponse, response);
            
            System.out.println("✓ Complex input: \"" + input.substring(0, Math.min(30, input.length())) + "...\"");
            System.out.println("  Response: \"" + response.substring(0, Math.min(40, response.length())) + "...\"");
        }
        
        // 验证所有调用都成功
        verify(bklightMockAgentService, times(complexInputs.length))
            .smartChatWithContext(eq(testUserId), anyString());
        
        System.out.println("=== Complex Expression Understanding Test Completed ===");
    }

    /**
     * 测试smartChat基本功能
     */
    @Test
    @DisplayName("测试smartChat基本功能")
    public void testSmartChatBasicFunctionality() {
        System.out.println("=== SmartChat Basic Functionality Test ===");
        
        // 模拟服务返回
        when(bklightMockAgentService.smartChatWithContext(anyString(), anyString()))
            .thenReturn("Mock response from smartChat");
        
        // 测试基本调用
        String response = bklightMockAgentService.smartChatWithContext(testUserId, "开始执行测试");
        
        // 验证结果
        assertNotNull(response);
        assertEquals("Mock response from smartChat", response);
        
        // 验证方法被调用
        verify(bklightMockAgentService, times(1))
            .smartChatWithContext(testUserId, "开始执行测试");
        
        System.out.println("✓ Basic functionality test passed");
        System.out.println("=== Test Completed ===");
    }

    /**
     * 测试smartChat意图识别功能
     */
    @Test
    @DisplayName("测试smartChat意图识别功能")
    public void testSmartChatIntentRecognition() {
        System.out.println("=== SmartChat Intent Recognition Test ===");
        
        // 测试不同意图的输入
        String[] testInputs = {
            "开始执行BKlight Mock测试",
            "查看当前执行状态", 
            "重试步骤3",
            "等待30分钟后检查注入结果",
            "清除当前进度，重新开始"
        };
        
        String[] expectedIntents = {
            "start_new_flow",
            "check_status",
            "retry_step:3", 
            "wait_check",
            "clear_restart"
        };
        
        // 模拟不同意图的响应
        for (int i = 0; i < testInputs.length; i++) {
            String input = testInputs[i];
            String expectedIntent = expectedIntents[i];
            
            when(bklightMockAgentService.smartChatWithContext(testUserId, input))
                .thenReturn("Intent recognized: " + expectedIntent);
            
            String response = bklightMockAgentService.smartChatWithContext(testUserId, input);
            
            assertNotNull(response);
            assertTrue(response.contains(expectedIntent));
            
            System.out.println("✓ Input: \"" + input + "\" -> Intent: " + expectedIntent);
        }
        
        System.out.println("=== Intent Recognition Test Completed ===");
    }

    /**
     * 测试smartChat参数验证功能
     */
    @Test
    @DisplayName("测试smartChat参数验证功能")
    public void testSmartChatParameterValidation() {
        System.out.println("=== SmartChat Parameter Validation Test ===");
        
        // 测试空参数
        when(bklightMockAgentService.smartChatWithContext(isNull(), anyString()))
            .thenReturn("❌ 错误：缺少必填参数 userId");
        
        when(bklightMockAgentService.smartChatWithContext(anyString(), isNull()))
            .thenReturn("❌ 错误：缺少用户输入");
        
        when(bklightMockAgentService.smartChatWithContext(anyString(), eq("")))
            .thenReturn("❌ 错误：缺少用户输入");
        
        // 测试空userId
        String response1 = bklightMockAgentService.smartChatWithContext(null, "test");
        assertNotNull(response1);
        assertTrue(response1.contains("缺少必填参数 userId"));
        
        // 测试空userInput
        String response2 = bklightMockAgentService.smartChatWithContext("user123", null);
        assertNotNull(response2);
        assertTrue(response2.contains("缺少用户输入"));
        
        // 测试空字符串userInput
        String response3 = bklightMockAgentService.smartChatWithContext("user123", "");
        assertNotNull(response3);
        assertTrue(response3.contains("缺少用户输入"));
        
        System.out.println("✓ Parameter validation tests passed");
        System.out.println("=== Parameter Validation Test Completed ===");
    }

    /**
     * 测试smartChat错误处理功能
     */
    @Test
    @DisplayName("测试smartChat错误处理功能")
    public void testSmartChatErrorHandling() {
        System.out.println("=== SmartChat Error Handling Test ===");
        
        // 模拟各种错误场景
        when(bklightMockAgentService.smartChatWithContext(testUserId, "invalid_command"))
            .thenReturn("未能识别您的意图，请提供更清晰的指令");
        
        when(bklightMockAgentService.smartChatWithContext(eq(""), anyString()))
            .thenReturn("❌ 错误：用户ID不能为空");
        
        // 模拟系统异常
        when(bklightMockAgentService.smartChatWithContext(testUserId, "system_error"))
            .thenReturn("系统暂时不可用，请稍后重试");
        
        // 测试无效命令
        String response1 = bklightMockAgentService.smartChatWithContext(testUserId, "invalid_command");
        assertNotNull(response1);
        assertTrue(response1.contains("未能识别"));
        
        // 测试空用户ID
        String response2 = bklightMockAgentService.smartChatWithContext("", "test");
        assertNotNull(response2);
        assertTrue(response2.contains("用户ID不能为空"));
        
        // 测试系统异常
        String response3 = bklightMockAgentService.smartChatWithContext(testUserId, "system_error");
        assertNotNull(response3);
        assertTrue(response3.contains("系统暂时不可用"));
        
        System.out.println("✓ Error handling tests passed");
        System.out.println("=== Error Handling Test Completed ===");
    }
}
