package com.alipay.findataquality.service.agent;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjsonfordrm.JSON;
import com.alipay.findataquality.service.dto.dependencyAnalyzer.EteExecResultDTO;
import com.alipay.findataquality.service.util.EteUtil;
import com.alipay.findataquality.service.util.HttpUtil;

import java.util.Comparator;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

public class MCPServerToolsTest {

    //新增测新规则
    public static final String ADD_NEW_PLAN_RULE_URL = "http://bklight.alipay.com/api/open/newPlan/addRule.json";
    //查询测新规则列表-获取绘制了链路依赖的规则
    public static final String QUERY_NEW_PLAN_RULE_URL = "http://bklight.alipay.com/api/open/newPlan/queryFlowRecord.json";
    //新增依赖规则并激活
    public static final String ADD_DEPENDENCY_RULE_URL = "http://bklight.alipay.com/api/open/dependence/analysis/addRule.json";
    //查询异常注入执行结果列表
    public static final String QUERY_DEPENDENCY_LIST_URL = "http://bklight.alipay.com/api/open/dependence/analysis/queryInjectDetails";
    //执行ETE结果
    public static final String ETE_EXECUTE_RET_API = "http://*************/model/caseFlowModelRun.json";
//    //查询ETE执行结果列表
//    public static final String ETE_QUERY_FLOW_EXECUTE_RET_API = "http://*************/running/queryFLowExcuteResult.json";

    public static void main(String[] args) {

        String caseId = "a9cd8ba6-f714-41ab-bad0-4022a2f60126";//64cbb482-e3ff-436c-854b-3b4037c814d9
        String caseName = "[余额宝][RPC]余额宝转出页渲染-新架构&新离线包[过yebtradebff]-黑卡+高客用户-提现页面渲染";//todo:人工or自动
        String downStreamInjectType = "PRC_TIME_OUT_EXCEPTION";//异常类型
        String  relatedMachineGroup = "GROUP_20250630104315";//机器组

        //1.增加测新规则：输入为caseId和caseName,输出为新建的测新规则newPlanRuleId
        String newPlanRuleId = toolsForAddNewPlan(caseId, caseName);

//        2.触发ETE执行
        boolean eteExecuteStatus = toolsForTriggerEte(caseId);

        //3.查询测新流量列表:需要在步骤2.「触发ETE执行」之前等待
//        String newPlanRuleId = "PLAN1749785601048";
        String caseInstanceId = toolsForQueryNewPlanList(caseId, newPlanRuleId);

//        4.新建依赖规则并激活:输入为caseInstanceId,输出为是否创建成功
//        String caseInstanceId = "d9c205c6-9afe-470e-a831-e672252a59a1";
        boolean addDependencyRuleStatus = toolsForAddDependencyRule(caseId,caseInstanceId, downStreamInjectType, relatedMachineGroup);

//        5.查询异常注入执行结果列表:需要在步骤4.「新建依赖规则并激活」之前等待
        String dependencyRuleId = "ef79c7281ffa46fd92f77636c8f8afc1";
        JSONArray injectResultList = toolsForQueryInjectResultList(dependencyRuleId);

    }

    /**
     * 工具用途：增加测新规则
     * 输入：caseId（用例id）,caseName（用例名称）
     * 输出：新建的测新规则id，即newPlanRuleId
     * @return
     */
    static String toolsForAddNewPlan(String caseId, String caseName){
        //1.增加测新规则：输入为caseId和caseName,输出为新建的测新规则newPlanRuleId
        Map<String, String> addNewPlanRuleRequest = new HashMap<>();
        addNewPlanRuleRequest.put("caseType", "E2E");
        addNewPlanRuleRequest.put("caseId", caseId);
        addNewPlanRuleRequest.put("caseName", caseName);
        addNewPlanRuleRequest.put("env", "DEV");
        String newPlanAddRuleResp = HttpUtil.sendHttpPostCommon(ADD_NEW_PLAN_RULE_URL, JSONObject.toJSONString(addNewPlanRuleRequest));
//        System.out.println("newPlanAddRuleResp:"+newPlanAddRuleResp);
        JSONObject newPlanAddRuleRespJsonObj = JSONObject.parseObject(newPlanAddRuleResp);
        String newPlanRuleId = newPlanAddRuleRespJsonObj.getJSONObject("data").getString("ruleId");
        boolean addNewPlanRuleStatus = newPlanAddRuleRespJsonObj.getJSONObject("data").getBoolean("success");
        System.out.println("adding new Plan Rule status: " + addNewPlanRuleStatus+",new plan rule id: "+newPlanRuleId);
        return newPlanRuleId;
    }

    /**
     * 工具用途：触发ETE（一个脚本执行引擎，支持http调用触发执行）执行
     * 输入：caseId（用例id）
     * 输出：ETE是否触发成功
     * @param caseId
     * @return
     */
    static boolean toolsForTriggerEte(String caseId) {
        //2.触发ETE执行
        Map<String, Object> requestParams = new HashMap<>();
        Map<String, Object> environmentVariables = new HashMap<>();
        environmentVariables.put("env", "dev");
        requestParams.put("environmentVariables", environmentVariables);

        Map<String, String> context = new HashMap<>();
        context.put("flowInstanceCode", caseId);
        requestParams.put("context", context);
        String req = JSON.toJSONString(requestParams);
        EteExecResultDTO triggerETEResp = EteUtil.executeEteTest(ETE_EXECUTE_RET_API, req);
        System.out.println("trigger ETE resp: " + JSON.toJSONString(triggerETEResp));
        boolean eteExecuteSuccess = triggerETEResp.getSuccess();
        System.out.println("trigger ETE status: " + eteExecuteSuccess);
        return eteExecuteSuccess;
    }

    /**
     * 工具用途：查询测新流量列表
     * 输入：caseId（用例id）和newPlanRuleId（测新规则ID）
     * 输出：依赖节点数最多的ete执行caseInstanceId(用例执行id)
     * @param caseId
     * @param newPlanRuleId
     * @return
     */
    static String toolsForQueryNewPlanList(String caseId,String newPlanRuleId) {
        //3.查询测新规则列表:输入为caseId和newPlanRuleId,输出为依赖节点数最多的ete执行caseInstanceId
        Map<String, String> queryNewPlanListRequest = new HashMap<>();
        queryNewPlanListRequest.put("caseId", caseId);
        queryNewPlanListRequest.put("ruleId", newPlanRuleId);
        System.out.println("queryNewPlanListRequest:"+JSON.toJSONString(queryNewPlanListRequest));
        String newPlanListResp = HttpUtil.sendHttpPostCommon(QUERY_NEW_PLAN_RULE_URL, JSONObject.toJSONString(queryNewPlanListRequest));
        System.out.println("newPlanListResp:"+newPlanListResp);
        JSONObject newPlanListRespJsonObj = JSONObject.parseObject(newPlanListResp);
        JSONArray newPlanList = newPlanListRespJsonObj.getJSONObject("data").getJSONArray("list");
        String caseInstanceId = Optional.ofNullable(newPlanList)
                .orElseGet(JSONArray::new)
                .stream()
                .filter(JSONObject.class::isInstance)
                .map(JSONObject.class::cast)
                .filter(item ->
                        "DEV".equals(item.getString("env")) &&
                                "E2E".equals(item.getString("caseType")) &&
                                "VALID".equals(item.getString("status")))
                .max(Comparator.comparingInt(
                        item -> item.getIntValue("flowFieldCount")))
                .map(item -> item.getString("caseInstanceId"))
                .orElse(null);
        boolean queryNewPlanListStatus = newPlanListRespJsonObj.getBoolean("success");
        System.out.println("query new Plan List status: " + queryNewPlanListStatus+",new Plan List:"+JSON.toJSONString(caseInstanceId));
        return caseInstanceId;
    }

    /**
     * 工具用途：新建依赖规则并激活.
     * 输入：caseId（用例id）,caseInstanceId（用例执行id）,downStreamInjectType（异常类型）,relatedMachineGroup(执行联调环境)
     * 输出：是否创建成功
     * @param caseId
     * @param caseInstanceId
     * @return
     */
    static boolean toolsForAddDependencyRule(String caseId,String caseInstanceId,String downStreamInjectType,String relatedMachineGroup) {
        //4.新建依赖规则并激活:输入为caseInstanceId,输出为是否创建成功
        Map<String, Object> addDependencyRuleRequest = new HashMap<>();
        addDependencyRuleRequest.put("caseId", caseId);
        addDependencyRuleRequest.put("caseType", "E2E");
        addDependencyRuleRequest.put("benchMarkFlowId", "root");
        addDependencyRuleRequest.put("benchMarkCaseInstanceId", caseInstanceId);
        addDependencyRuleRequest.put("downStreamInjectType", downStreamInjectType);
        addDependencyRuleRequest.put("machineGroupEnv", "DEV");
        addDependencyRuleRequest.put("relatedMachineGroup", relatedMachineGroup);
        addDependencyRuleRequest.put("enableAutoActivate",true);
        String dependencyAddRuleResp = HttpUtil.sendHttpPostCommon(ADD_DEPENDENCY_RULE_URL, JSONObject.toJSONString(addDependencyRuleRequest));
        System.out.println("dependencyAddRuleResp: " + dependencyAddRuleResp);
        JSONObject dependencyAddRuleRespJsonObj = JSONObject.parseObject(dependencyAddRuleResp);
        boolean addDependencyStatus = dependencyAddRuleRespJsonObj.getBoolean("success");
        System.out.println("adding dependency rule status: " + addDependencyStatus);
        return addDependencyStatus;
    }

    /**
     * 工具用途：查询异常注入执行结果列表
     * 输入：dependencyRuleId（依赖配置规则id）
     * 输出：查询结果列表
     * @return
     */
    static JSONArray toolsForQueryInjectResultList(String dependencyRuleId) {
        Map<String, String> queryInjectResultListRequest = new HashMap<>();
        queryInjectResultListRequest.put("id", dependencyRuleId);
        String injectResultListResp = HttpUtil.sendHttpPostCommon(QUERY_DEPENDENCY_LIST_URL, JSONObject.toJSONString(queryInjectResultListRequest));
        System.out.println("injectResultListResp:"+injectResultListResp);
        JSONObject injectResultListRespJsonObj = JSONObject.parseObject(injectResultListResp);
        JSONArray injectResultList = injectResultListRespJsonObj.getJSONObject("data").getJSONArray("elements");
        System.out.println("injectResultList:"+JSON.toJSONString(injectResultList));
        boolean queryInjectResultListStatus = injectResultListRespJsonObj.getBoolean("success");
        System.out.println("query Inject Result status:"+queryInjectResultListStatus+",List: " + JSON.toJSONString(injectResultList));
        // 1. 过滤出 status=SUCCESS 的元素
        JSONArray successArray = injectResultList.stream()
                .filter(obj -> obj instanceof JSONObject) // 确保元素为JSONObject
                .map(obj -> (JSONObject) obj)
                .filter(jsonObj -> {
                    String status = jsonObj.getString("status");
                    return status != null && status.equals("SUCCESS");
                })
                .collect(
                        JSONArray::new,          // Supplier：创建新JSONArray
                        JSONArray::add,          // Accumulator：逐个添加元素
                        JSONArray::addAll        // Combiner：并行流合并
                );
        // 2.统计非SUCCESS数量
        int nonSuccessCount = injectResultList.size() - successArray.size();
        System.out.println("query Inject Result nonSuccessCount:"+nonSuccessCount);

        return successArray;
    }

}
