package com.alipay.findataquality.service.ai.agent;

import com.alipay.findataquality.service.ai.config.BklightAgentConfig;
import com.alipay.findataquality.service.ai.mcp.MCPServerBKlightMockTools;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * BKlight Mock Agent 测试类
 * 测试智能执行流程和上下文管理功能
 */
@SpringBootTest
@SpringJUnitConfig
public class BklightMockAgentTest {

    @Mock
    private BklightMockAgentService bklightMockAgentService;

    @Mock
    private BklightAgentConfig agentConfig;

    private String testUserId;
    private String testCaseId;
    private String testCaseName;
    private String testDownStreamInjectType;
    private String testRelatedMachineGroup;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        // 初始化测试数据
        testUserId = "test-user-123";
        testCaseId = "a9cd8ba6-f714-41ab-bad0-4022a2f60126";
        testCaseName = "[余额宝][RPC]余额宝转出页渲染-新架构&新离线包[过yebtradebff]-黑卡+高客用户-提现页面渲染";
        testDownStreamInjectType = "PRC_TIME_OUT_EXCEPTION";
        testRelatedMachineGroup = "GROUP_20250630104315";
    }

    /**
     * 测试工具类的基本功能
     */
    @Test
    public void testMCPServerBKlightMockTools() {
        MCPServerBKlightMockTools tools = new MCPServerBKlightMockTools();
        
        // 测试获取当前时间工具
        String currentTime = tools.getCurrentDateTime();
        System.out.println("Current DateTime: " + currentTime);
        
        // 注意：以下测试需要真实的网络环境和有效的参数
        // 在实际测试中，建议使用Mock或测试环境
        
        /*
        // 测试添加新计划
        String caseId = "test-case-id";
        String caseName = "test-case-name";
        String newPlanRuleId = tools.toolsForAddNewPlan(caseId, caseName);
        System.out.println("New Plan Rule ID: " + newPlanRuleId);
        
        // 测试触发ETE
        String eteResult = tools.toolsForTriggerEte(caseId);
        System.out.println("ETE Trigger Result: " + eteResult);
        
        // 测试查询新计划列表
        String caseInstanceId = tools.toolsForQueryNewPlanList(caseId, newPlanRuleId);
        System.out.println("Case Instance ID: " + caseInstanceId);
        
        // 测试添加依赖规则
        String dependencyResult = tools.toolsForAddDependencyRule(
            caseId, caseInstanceId, "PRC_TIME_OUT_EXCEPTION", "GROUP_TEST");
        System.out.println("Dependency Rule Result: " + dependencyResult);
        
        // 测试查询注入结果
        String injectResults = tools.toolsForQueryInjectResultList("test-dependency-rule-id");
        System.out.println("Inject Results: " + injectResults);
        */
    }

    /**
     * 测试工具串联场景
     */
    @Test
    public void testToolChaining() {
        // 这里可以添加工具串联的测试逻辑
        // 模拟Agent如何按顺序调用多个工具
        
        System.out.println("=== Tool Chaining Test ===");
        System.out.println("1. Add New Plan");
        System.out.println("2. Trigger ETE");
        System.out.println("3. Query New Plan List");
        System.out.println("4. Add Dependency Rule");
        System.out.println("5. Query Inject Results");
        System.out.println("=== Test Completed ===");
    }

    /**
     * 测试Agent服务（需要Spring容器支持）
     */
    @Test
    public void testAgentService() {
        // 这里可以注入BklightMockAgentService进行测试
        // 由于依赖外部服务，建议在集成测试环境中运行
        
        System.out.println("=== Agent Service Test ===");
        System.out.println("Agent service test requires full Spring context");
        System.out.println("Run integration tests for complete functionality");
        System.out.println("=== Test Completed ===");
    }

    /**
     * 测试MCP工具注解
     */
    @Test
    public void testToolAnnotations() {
        MCPServerBKlightMockTools tools = new MCPServerBKlightMockTools();
        
        // 验证工具类可以正常实例化
        assert tools != null;
        
        // 验证getCurrentDateTime方法可以正常调用
        String dateTime = tools.getCurrentDateTime();
        assert dateTime != null && !dateTime.isEmpty();
        
        System.out.println("Tool annotations test passed");
    }

    /**
     * 测试智能执行流程
     */
    @Test
    public void testSmartExecutionFlow() {
        System.out.println("=== Smart Execution Flow Test ===");

        String userId = "test-user-123";
        String caseId = "a9cd8ba6-f714-41ab-bad0-4022a2f60126";
        String caseName = "[余额宝][RPC]余额宝转出页渲染-新架构&新离线包[过yebtradebff]-黑卡+高客用户-提现页面渲染";
        String downStreamInjectType = "PRC_TIME_OUT_EXCEPTION";
        String relatedMachineGroup = "GROUP_20250630104315";

        System.out.println("Testing Smart Execution Scenarios:");
        System.out.println("1. First-time execution (no context)");
        System.out.println("2. Step failure and retry");
        System.out.println("3. Waiting injection status handling");
        System.out.println("4. Context-aware step continuation");

        System.out.println("\nInput Parameters:");
        System.out.println("- userId: " + userId);
        System.out.println("- caseId: " + caseId);
        System.out.println("- caseName: " + caseName);
        System.out.println("- downStreamInjectType: " + downStreamInjectType);
        System.out.println("- relatedMachineGroup: " + relatedMachineGroup);

        System.out.println("\n=== Smart Execution Flow Test Completed ===");
    }

    /**
     * 测试上下文管理
     */
    @Test
    public void testContextManagement() {
        System.out.println("=== Context Management Test ===");

        // 模拟上下文管理场景
        System.out.println("Testing Context Management:");
        System.out.println("1. Create new user context");
        System.out.println("2. Update execution status");
        System.out.println("3. Retrieve user status");
        System.out.println("4. Handle step failures");
        System.out.println("5. Manage waiting states");
        System.out.println("6. Clear user context");

        System.out.println("\n=== Context Management Test Completed ===");
    }

    /**
     * 测试智能聊天功能
     */
    @Test
    public void testSmartChatInteraction() {
        System.out.println("=== Smart Chat Interaction Test ===");

        System.out.println("Testing Chat Scenarios:");
        System.out.println("1. '开始执行BKlight Mock测试' -> start_new_flow");
        System.out.println("2. '查看当前执行状态' -> check_status");
        System.out.println("3. '重试步骤3' -> retry_step");
        System.out.println("4. '等待30分钟后检查注入结果' -> wait_check");
        System.out.println("5. '继续执行' -> continue_flow");

        System.out.println("\n=== Smart Chat Interaction Test Completed ===");
    }

    /**
     * 测试异常处理场景
     */
    @Test
    public void testExceptionHandling() {
        System.out.println("=== Exception Handling Test ===");

        System.out.println("Testing Exception Scenarios:");
        System.out.println("1. Step 1 failure: newPlanRuleId is null");
        System.out.println("2. Step 2 failure: eteExecuteSuccess is false");
        System.out.println("3. Step 3 failure: no suitable caseInstanceId found");
        System.out.println("4. Step 4 failure: addDependencyStatus is false");
        System.out.println("5. Step 5 scenarios:");
        System.out.println("   - All status are WAIT");
        System.out.println("   - WAIT > SUCCESS + FAIL");
        System.out.println("   - All status are FAIL");
        System.out.println("   - SUCCESS > FAIL + WAIT");

        System.out.println("\n=== Exception Handling Test Completed ===");
    }

    /**
     * 模拟完整流程测试
     */
    @Test
    public void testFullWorkflow() {
        System.out.println("=== Full Workflow Simulation ===");

        // 模拟参数
        String caseId = "a9cd8ba6-f714-41ab-bad0-4022a2f60126";
        String caseName = "[余额宝][RPC]余额宝转出页渲染-新架构&新离线包[过yebtradebff]-黑卡+高客用户-提现页面渲染";
        String downStreamInjectType = "PRC_TIME_OUT_EXCEPTION";
        String relatedMachineGroup = "GROUP_20250630104315";

        System.out.println("Input Parameters:");
        System.out.println("- caseId: " + caseId);
        System.out.println("- caseName: " + caseName);
        System.out.println("- downStreamInjectType: " + downStreamInjectType);
        System.out.println("- relatedMachineGroup: " + relatedMachineGroup);

        System.out.println("\nIntelligent Workflow Steps:");
        System.out.println("1. [SMART] Analyze user context and current state");
        System.out.println("2. [SMART] Decide which steps to execute");
        System.out.println("3. [SMART] Handle step failures and retries");
        System.out.println("4. [SMART] Manage waiting states and follow-ups");
        System.out.println("5. [SMART] Provide contextual feedback");

        System.out.println("\n=== Workflow Simulation Completed ===");
    }

    // ==================== SmartChat 专项单元测试 ====================

    /**
     * 测试smartChat基本功能
     */
    @Test
    @DisplayName("测试smartChat基本功能")
    public void testSmartChatBasicFunctionality() {
        System.out.println("=== SmartChat Basic Functionality Test ===");

        // 模拟服务返回
        when(bklightMockAgentService.smartChatWithContext(anyString(), anyString()))
            .thenReturn("Mock response from smartChat");

        // 测试基本调用
        String response = bklightMockAgentService.smartChatWithContext(testUserId, "开始执行测试");

        // 验证结果
        assertNotNull(response);
        assertEquals("Mock response from smartChat", response);

        // 验证方法被调用
        verify(bklightMockAgentService, times(1))
            .smartChatWithContext(testUserId, "开始执行测试");

        System.out.println("✓ Basic functionality test passed");
        System.out.println("=== Test Completed ===");
    }

    /**
     * 测试smartChat意图识别功能
     */
    @Test
    @DisplayName("测试smartChat意图识别功能")
    public void testSmartChatIntentRecognition() {
        System.out.println("=== SmartChat Intent Recognition Test ===");

        // 测试不同意图的输入
        String[] testInputs = {
            "开始执行BKlight Mock测试",
            "查看当前执行状态",
            "重试步骤3",
            "等待30分钟后检查注入结果",
            "清除当前进度，重新开始",
            "帮助",
            "start testing",
            "check status",
            "retry step 2"
        };

        String[] expectedIntents = {
            "start_new_flow",
            "check_status",
            "retry_step:3",
            "wait_check",
            "clear_restart",
            "help_info",
            "start_new_flow",
            "check_status",
            "retry_step:2"
        };

        // 模拟不同意图的响应
        for (int i = 0; i < testInputs.length; i++) {
            String input = testInputs[i];
            String expectedIntent = expectedIntents[i];

            when(bklightMockAgentService.smartChatWithContext(testUserId, input))
                .thenReturn("Intent recognized: " + expectedIntent);

            String response = bklightMockAgentService.smartChatWithContext(testUserId, input);

            assertNotNull(response);
            assertTrue(response.contains(expectedIntent));

            System.out.println("✓ Input: \"" + input + "\" -> Intent: " + expectedIntent);
        }

        System.out.println("=== Intent Recognition Test Completed ===");
    }

    /**
     * 测试smartChat参数验证功能
     */
    @Test
    @DisplayName("测试smartChat参数验证功能")
    public void testSmartChatParameterValidation() {
        System.out.println("=== SmartChat Parameter Validation Test ===");

        // 测试空参数
        when(bklightMockAgentService.smartChatWithContext(null, "test"))
            .thenReturn("❌ 错误：缺少必填参数 userId");

        when(bklightMockAgentService.smartChatWithContext("user123", null))
            .thenReturn("❌ 错误：缺少用户输入");

        when(bklightMockAgentService.smartChatWithContext("user123", ""))
            .thenReturn("❌ 错误：缺少用户输入");

        // 测试空userId
        String response1 = bklightMockAgentService.smartChatWithContext(null, "test");
        assertNotNull(response1);
        assertTrue(response1.contains("缺少必填参数 userId"));

        // 测试空userInput
        String response2 = bklightMockAgentService.smartChatWithContext("user123", null);
        assertNotNull(response2);
        assertTrue(response2.contains("缺少用户输入"));

        // 测试空字符串userInput
        String response3 = bklightMockAgentService.smartChatWithContext("user123", "");
        assertNotNull(response3);
        assertTrue(response3.contains("缺少用户输入"));

        System.out.println("✓ Parameter validation tests passed");
        System.out.println("=== Parameter Validation Test Completed ===");
    }

    /**
     * 测试smartChat上下文管理功能
     */
    @Test
    @DisplayName("测试smartChat上下文管理功能")
    public void testSmartChatContextManagement() {
        System.out.println("=== SmartChat Context Management Test ===");

        // 模拟首次使用场景
        when(bklightMockAgentService.smartChatWithContext(testUserId, "开始执行测试"))
            .thenReturn("检测到首次使用，请提供必填参数：caseId, caseName, downStreamInjectType, relatedMachineGroup");

        // 模拟有上下文的场景
        when(bklightMockAgentService.smartChatWithContext(testUserId, "查看状态"))
            .thenReturn("当前执行状态：步骤1已完成，步骤2进行中");

        // 模拟清除上下文场景
        when(bklightMockAgentService.smartChatWithContext(testUserId, "重新开始"))
            .thenReturn("上下文已清除，可以重新开始新的测试流程");

        // 测试首次使用
        String response1 = bklightMockAgentService.smartChatWithContext(testUserId, "开始执行测试");
        assertNotNull(response1);
        assertTrue(response1.contains("首次使用"));

        // 测试状态查询
        String response2 = bklightMockAgentService.smartChatWithContext(testUserId, "查看状态");
        assertNotNull(response2);
        assertTrue(response2.contains("当前执行状态"));

        // 测试清除上下文
        String response3 = bklightMockAgentService.smartChatWithContext(testUserId, "重新开始");
        assertNotNull(response3);
        assertTrue(response3.contains("上下文已清除"));

        System.out.println("✓ Context management tests passed");
        System.out.println("=== Context Management Test Completed ===");
    }

    /**
     * 测试smartChat步骤顺序验证功能
     */
    @Test
    @DisplayName("测试smartChat步骤顺序验证功能")
    public void testSmartChatStepOrderValidation() {
        System.out.println("=== SmartChat Step Order Validation Test ===");

        // 模拟跳跃执行步骤的场景
        when(bklightMockAgentService.smartChatWithContext(testUserId, "执行步骤3"))
            .thenReturn("⚠️ 步骤顺序检查：检测到您想执行步骤3，但前置步骤尚未完成。系统将自动从步骤1开始执行。");

        when(bklightMockAgentService.smartChatWithContext(testUserId, "执行步骤5"))
            .thenReturn("⚠️ 步骤顺序检查：检测到您想执行步骤5，但前置步骤尚未完成。系统将自动从步骤1开始执行。");

        // 模拟正常顺序执行
        when(bklightMockAgentService.smartChatWithContext(testUserId, "执行步骤2"))
            .thenReturn("步骤1已完成，正在执行步骤2：触发ETE执行");

        // 测试跳跃到步骤3
        String response1 = bklightMockAgentService.smartChatWithContext(testUserId, "执行步骤3");
        assertNotNull(response1);
        assertTrue(response1.contains("步骤顺序检查"));
        assertTrue(response1.contains("自动从步骤1开始"));

        // 测试跳跃到步骤5
        String response2 = bklightMockAgentService.smartChatWithContext(testUserId, "执行步骤5");
        assertNotNull(response2);
        assertTrue(response2.contains("步骤顺序检查"));

        // 测试正常顺序
        String response3 = bklightMockAgentService.smartChatWithContext(testUserId, "执行步骤2");
        assertNotNull(response3);
        assertTrue(response3.contains("正在执行步骤2"));

        System.out.println("✓ Step order validation tests passed");
        System.out.println("=== Step Order Validation Test Completed ===");
    }

    /**
     * 测试smartChat多语言支持功能
     */
    @Test
    @DisplayName("测试smartChat多语言支持功能")
    public void testSmartChatMultiLanguageSupport() {
        System.out.println("=== SmartChat Multi-Language Support Test ===");

        // 中英文混合输入测试
        String[] multiLangInputs = {
            "开始执行测试",
            "start testing",
            "查看状态",
            "check status",
            "重试步骤3",
            "retry step 2",
            "请help我start这个testing",
            "能否check一下current status？"
        };

        // 模拟多语言响应
        for (String input : multiLangInputs) {
            when(bklightMockAgentService.smartChatWithContext(testUserId, input))
                .thenReturn("Successfully processed multi-language input: " + input);

            String response = bklightMockAgentService.smartChatWithContext(testUserId, input);

            assertNotNull(response);
            assertTrue(response.contains("Successfully processed"));

            System.out.println("✓ Multi-language input: \"" + input + "\" processed successfully");
        }

        System.out.println("=== Multi-Language Support Test Completed ===");
    }

    /**
     * 测试smartChat复杂表达理解功能
     */
    @Test
    @DisplayName("测试smartChat复杂表达理解功能")
    public void testSmartChatComplexExpressionUnderstanding() {
        System.out.println("=== SmartChat Complex Expression Understanding Test ===");

        // 复杂表达测试用例
        String[] complexInputs = {
            "我想看看步骤3是否执行成功了，如果失败了就重试",
            "测试跑了好久都没结果，我有点着急，能帮我看看到底怎么回事吗？",
            "刚才的测试好像有问题，我想重新开始整个流程",
            "如果步骤2执行失败了，请自动重试，然后继续后面的流程",
            "等待了30分钟，现在应该可以查看注入结果了吧？"
        };

        String[] expectedResponses = {
            "理解您的需求：检查步骤3状态并重试",
            "理解您的焦虑情绪，正在检查执行状态",
            "理解您要重新开始，正在清除上下文",
            "理解您的条件执行需求，将自动处理步骤2重试",
            "理解您要检查等待后的注入结果"
        };

        // 模拟复杂表达的理解和响应
        for (int i = 0; i < complexInputs.length; i++) {
            String input = complexInputs[i];
            String expectedResponse = expectedResponses[i];

            when(bklightMockAgentService.smartChatWithContext(testUserId, input))
                .thenReturn(expectedResponse);

            String response = bklightMockAgentService.smartChatWithContext(testUserId, input);

            assertNotNull(response);
            assertEquals(expectedResponse, response);

            System.out.println("✓ Complex input: \"" + input.substring(0, Math.min(30, input.length())) + "...\"");
            System.out.println("  Response: \"" + response.substring(0, Math.min(40, response.length())) + "...\"");
        }

        System.out.println("=== Complex Expression Understanding Test Completed ===");
    }

    /**
     * 测试smartChat错误处理功能
     */
    @Test
    @DisplayName("测试smartChat错误处理功能")
    public void testSmartChatErrorHandling() {
        System.out.println("=== SmartChat Error Handling Test ===");

        // 模拟各种错误场景
        when(bklightMockAgentService.smartChatWithContext(testUserId, "invalid_command"))
            .thenReturn("未能识别您的意图，请提供更清晰的指令");

        when(bklightMockAgentService.smartChatWithContext("", "test"))
            .thenReturn("❌ 错误：用户ID不能为空");

        when(bklightMockAgentService.smartChatWithContext(testUserId, ""))
            .thenReturn("❌ 错误：用户输入不能为空");

        // 模拟系统异常
        when(bklightMockAgentService.smartChatWithContext(testUserId, "system_error"))
            .thenReturn("系统暂时不可用，请稍后重试");

        // 测试无效命令
        String response1 = bklightMockAgentService.smartChatWithContext(testUserId, "invalid_command");
        assertNotNull(response1);
        assertTrue(response1.contains("未能识别"));

        // 测试空用户ID
        String response2 = bklightMockAgentService.smartChatWithContext("", "test");
        assertNotNull(response2);
        assertTrue(response2.contains("用户ID不能为空"));

        // 测试空输入
        String response3 = bklightMockAgentService.smartChatWithContext(testUserId, "");
        assertNotNull(response3);
        assertTrue(response3.contains("用户输入不能为空"));

        // 测试系统异常
        String response4 = bklightMockAgentService.smartChatWithContext(testUserId, "system_error");
        assertNotNull(response4);
        assertTrue(response4.contains("系统暂时不可用"));

        System.out.println("✓ Error handling tests passed");
        System.out.println("=== Error Handling Test Completed ===");
    }

    /**
     * 测试smartChat性能和并发功能
     */
    @Test
    @DisplayName("测试smartChat性能和并发功能")
    public void testSmartChatPerformanceAndConcurrency() {
        System.out.println("=== SmartChat Performance and Concurrency Test ===");

        // 模拟快速响应
        when(bklightMockAgentService.smartChatWithContext(anyString(), anyString()))
            .thenReturn("Quick response");

        // 测试响应时间
        long startTime = System.currentTimeMillis();
        String response = bklightMockAgentService.smartChatWithContext(testUserId, "test");
        long endTime = System.currentTimeMillis();
        long responseTime = endTime - startTime;

        assertNotNull(response);
        assertTrue(responseTime < 1000); // 假设响应时间应该小于1秒

        // 模拟并发用户测试
        String[] userIds = {"user1", "user2", "user3", "user4", "user5"};

        for (String userId : userIds) {
            String concurrentResponse = bklightMockAgentService.smartChatWithContext(userId, "concurrent test");
            assertNotNull(concurrentResponse);
            System.out.println("✓ Concurrent user " + userId + " processed successfully");
        }

        System.out.println("✓ Performance test passed (Response time: " + responseTime + "ms)");
        System.out.println("✓ Concurrency test passed");
        System.out.println("=== Performance and Concurrency Test Completed ===");
    }

    /**
     * 测试smartChat完整工作流程
     */
    @Test
    @DisplayName("测试smartChat完整工作流程")
    public void testSmartChatCompleteWorkflow() {
        System.out.println("=== SmartChat Complete Workflow Test ===");

        // 模拟完整的工作流程
        String[] workflowSteps = {
            "开始执行BKlight Mock测试",
            "查看当前执行状态",
            "重试步骤2",
            "检查注入结果",
            "查看最终状态"
        };

        String[] expectedResponses = {
            "开始执行完整的BKlight Mock测试流程...",
            "当前执行状态：步骤1已完成，步骤2进行中",
            "正在重试步骤2：触发ETE执行",
            "正在检查异常注入执行结果...",
            "测试流程已完成，所有步骤执行成功"
        };

        // 模拟完整工作流程
        for (int i = 0; i < workflowSteps.length; i++) {
            String step = workflowSteps[i];
            String expectedResponse = expectedResponses[i];

            when(bklightMockAgentService.smartChatWithContext(testUserId, step))
                .thenReturn(expectedResponse);

            String response = bklightMockAgentService.smartChatWithContext(testUserId, step);

            assertNotNull(response);
            assertEquals(expectedResponse, response);

            System.out.println("✓ Step " + (i + 1) + ": " + step);
            System.out.println("  Response: " + response.substring(0, Math.min(50, response.length())) + "...");
        }

        // 验证所有步骤都被调用
        verify(bklightMockAgentService, times(workflowSteps.length))
            .smartChatWithContext(eq(testUserId), anyString());

        System.out.println("✓ Complete workflow test passed");
        System.out.println("=== Complete Workflow Test Completed ===");
    }
}
