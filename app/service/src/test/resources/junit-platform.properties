# JUnit Platform 配置文件

# 测试执行配置
junit.jupiter.execution.parallel.enabled=false
junit.jupiter.execution.parallel.mode.default=concurrent

# 测试发现配置
junit.jupiter.testinstance.lifecycle.default=per_method

# 显示名称配置
junit.jupiter.displayname.generator.default=org.junit.jupiter.api.DisplayNameGenerator$ReplaceUnderscores

# 条件测试配置
junit.jupiter.conditions.deactivate=org.junit.*DisabledCondition

# 扩展配置
junit.jupiter.extensions.autodetection.enabled=true

# 日志配置
java.util.logging.manager=org.apache.logging.log4j.jul.LogManager
