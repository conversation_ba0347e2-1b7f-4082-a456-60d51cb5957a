# BKlight Mock Agent 使用指南

## 概述

BKlight Mock Agent 是一个基于MCP (Model Context Protocol) 协议的智能代理系统，能够串联多个工具来自动化执行BKlight Mock测试流程。该系统支持上下文感知、智能步骤管理和异常处理，能够根据执行状态智能决定下一步操作。

## 🆕 新特性

- **上下文感知**: 系统记住用户的执行历史和当前状态
- **智能步骤管理**: 根据执行情况自动决定重试或继续
- **异常处理**: 详细的错误分析和处理建议
- **等待状态管理**: 智能处理异步操作的等待状态
- **自然语言交互**: 支持中英文自然语言交互

## 架构设计

### 核心组件

1. **MCPServerBKlightMockTools** - 工具注册类，包含5个核心工具
2. **BklightMockAgentService** - Agent服务类，提供工具串联能力
3. **BklightMockAgentController** - REST API控制器
4. **McpClientController** - MCP客户端控制器，提供基础的MCP交互能力

### 工具列表

| 工具名称 | 描述 | 输入参数 | 输出 |
|---------|------|---------|------|
| `addNewPlan` | 添加新的测试计划规则 | caseId, caseName | newPlanRuleId |
| `triggerEte` | 触发ETE执行 | caseId | 执行状态 |
| `queryNewPlanList` | 查询测新流量列表 | caseId, newPlanRuleId | caseInstanceId |
| `addDependencyRule` | 添加依赖规则并激活 | caseId, caseInstanceId, downStreamInjectType, relatedMachineGroup | 创建状态 |
| `queryInjectResultList` | 查询异常注入执行结果 | dependencyRuleId | 成功注入结果列表 |

## 智能执行流程

### 执行逻辑

系统根据以下逻辑智能决定执行步骤：

1. **首次使用**: 如果上下文中不存在任何步骤记录，执行完整流程（步骤1-5）
2. **步骤失败重试**: 如果某个步骤失败，从该步骤重新开始执行
3. **等待状态处理**: 对于异步操作，智能管理等待状态和重试时机
4. **上下文继承**: 保持用户会话状态，支持分步骤交互

### 详细步骤说明

**步骤1**: 增加测新规则
- 成功条件: `newPlanRuleId` 不为null
- 失败处理: 返回详细错误原因

**步骤2**: 触发ETE执行
- 成功条件: `eteExecuteSuccess` 为true
- 失败处理: 返回详细错误原因

**步骤3**: 查询测新流量列表
- 成功条件: 找到`flowFieldCount > 0`（最好>10）的`caseInstanceId`
- 失败处理: 循环执行步骤2-3直到找到完整依赖树

**步骤4**: 新增依赖配置规则
- 成功条件: `addDependencyStatus` 为true
- 失败处理: 返回详细错误原因

**步骤5**: 查询异常注入执行结果
- 全部WAIT: "注入尚未开始，需要再等待10-30分钟再单独执行该步骤"
- WAIT多于SUCCESS+FAIL: "需要再等待10-30分钟再单独执行该步骤"
- 全部FAIL: "注入失败，需bklight人员排查"
- SUCCESS多于FAIL+WAIT: 返回详细统计，对WAIT状态提示等待

**步骤6**: 等待后重新检查
- 针对步骤5中有WAIT状态的情况，等待后重新执行检查

## 使用方式

### 1. 智能执行（推荐）

通过智能接口执行，系统会根据上下文自动决定执行步骤：

```bash
curl -X POST http://localhost:8080/api/bklight/agent/smart-execute \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "user123",
    "caseId": "a9cd8ba6-f714-41ab-bad0-4022a2f60126",
    "caseName": "[余额宝][RPC]余额宝转出页渲染-新架构&新离线包[过yebtradebff]-黑卡+高客用户-提现页面渲染",
    "downStreamInjectType": "PRC_TIME_OUT_EXCEPTION",
    "relatedMachineGroup": "GROUP_20250630104315"
  }'
```

### 2. 智能聊天交互（推荐）

通过自然语言与Agent交互：

```bash
# 开始执行
curl -X POST "http://localhost:8080/api/bklight/agent/smart-chat?userId=user123&userInput=开始执行BKlight Mock测试"

# 检查状态
curl -X POST "http://localhost:8080/api/bklight/agent/smart-chat?userId=user123&userInput=查看当前执行状态"

# 重试步骤
curl -X POST "http://localhost:8080/api/bklight/agent/smart-chat?userId=user123&userInput=重试步骤3"

# 等待后检查
curl -X POST "http://localhost:8080/api/bklight/agent/smart-chat?userId=user123&userInput=等待30分钟后检查注入结果"
```

### 3. 执行特定步骤

```bash
curl -X POST "http://localhost:8080/api/bklight/agent/execute-specific-step?userId=user123&stepNumber=3"
```

### 4. 获取执行状态

```bash
curl -X GET "http://localhost:8080/api/bklight/agent/status?userId=user123"
```

### 5. 完整流程执行（兼容性）

```bash
curl -X POST http://localhost:8080/api/bklight/agent/execute-full-flow \
  -H "Content-Type: application/json" \
  -d '{
    "caseId": "a9cd8ba6-f714-41ab-bad0-4022a2f60126",
    "caseName": "[余额宝][RPC]余额宝转出页渲染-新架构&新离线包[过yebtradebff]-黑卡+高客用户-提现页面渲染",
    "downStreamInjectType": "PRC_TIME_OUT_EXCEPTION",
    "relatedMachineGroup": "GROUP_20250630104315"
  }'
```

### 2. 单步执行

执行单个步骤：

```bash
curl -X POST "http://localhost:8080/api/bklight/agent/execute-step?stepName=addNewPlan&parameters=caseId:test123,caseName:testCase"
```

### 3. 自定义Agent交互

通过自然语言与Agent交互：

```bash
curl -X POST "http://localhost:8080/api/bklight/agent/chat?prompt=请帮我创建一个新的测试计划，用例ID是test123"
```

### 4. 获取可用工具

```bash
curl -X GET http://localhost:8080/api/bklight/agent/tools
```

### 5. 直接使用MCP客户端

```bash
curl -X GET "http://localhost:8080/mcp/client/chat?input=请使用addNewPlan工具创建一个新的测试计划"
```

## 工具串联示例

### 场景1：完整的自动化测试流程

```
用户输入: "请执行完整的BKlight Mock测试流程"

Agent执行步骤:
1. addNewPlan(caseId, caseName) -> 获得newPlanRuleId
2. triggerEte(caseId) -> 触发ETE执行
3. queryNewPlanList(caseId, newPlanRuleId) -> 获得caseInstanceId
4. addDependencyRule(caseId, caseInstanceId, downStreamInjectType, relatedMachineGroup) -> 创建依赖规则
5. queryInjectResultList(dependencyRuleId) -> 查询注入结果
```

### 场景2：条件化执行

```
用户输入: "如果ETE执行成功，则继续创建依赖规则"

Agent执行逻辑:
1. triggerEte(caseId)
2. 根据返回结果判断是否继续
3. 如果成功，执行addDependencyRule
```

## 配置说明

### MCP服务器配置

在 `application.properties` 中确保以下配置：

```properties
# MCP Server 配置
sofa.ai.mcp.server.enabled=true
sofa.ai.mcp.server.name=findataqualityMcpServer
sofa.ai.mcp.server.version=1.0.0

# MCP Client 配置
sofa.ai.mcp.client.toolcallback.enabled=true

# AntLLM 配置
sofa.ai.antllm.chat.app-token=your-app-token
sofa.ai.antllm.chat.options.model=qwen3-235b-a22b
```

### 工具注册

工具通过 `McpServerToolConfiguration` 自动注册：

```java
@Configuration(proxyBeanMethods = false)
public class McpServerToolConfiguration {
    @Bean
    public ToolCallbackProvider myTools() {
        List<ToolCallback> tools = Arrays.asList(ToolCallbacks.from(new MCPServerBKlightMockTools()));
        return ToolCallbackProvider.from(tools);
    }
}
```

## 扩展开发

### 添加新工具

1. 在 `MCPServerBKlightMockTools` 类中添加新方法
2. 使用 `@Tool` 注解标记
3. 提供英文描述和参数说明

```java
@Tool(name="newTool", description = "Description of the new tool")
String newTool(String param1, String param2) {
    // 实现逻辑
    return result;
}
```

### 自定义Agent逻辑

在 `BklightMockAgentService` 中添加新的业务方法：

```java
public String customBusinessFlow(String... params) {
    String prompt = "Custom business logic prompt";
    return chatWithMcp(prompt);
}
```

## 注意事项

1. **异步执行**: ETE执行是异步的，需要适当的等待时间
2. **错误处理**: 每个工具都有内置的错误处理机制
3. **参数验证**: 确保传入的参数格式正确
4. **网络依赖**: 工具依赖外部API，需要确保网络连通性

## 故障排查

### 常见问题

1. **MCP客户端未初始化**: 检查配置文件中的MCP相关配置
2. **工具调用失败**: 检查网络连接和API端点
3. **参数格式错误**: 确保JSON格式正确

### 日志查看

查看应用日志获取详细的执行信息：

```bash
tail -f logs/application.log | grep -E "(MCP|Tool|Agent)"
```
