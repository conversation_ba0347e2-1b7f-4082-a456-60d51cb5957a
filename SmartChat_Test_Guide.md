# SmartChat 单元测试指南

## 🎯 测试概述

本文档介绍了为BKlight Mock Agent的`smartChat`功能创建的完整单元测试套件。

## 📁 测试文件结构

```
app/service/src/test/java/com/alipay/findataquality/service/ai/agent/
├── BklightMockAgentTest.java          # 原有测试类（已更新）
├── SmartChatUnitTest.java             # SmartChat专项单元测试
└── SmartChatIntegrationTest.java      # SmartChat集成测试

app/service/src/test/resources/
├── application-test.properties        # 测试环境配置
└── junit-platform.properties         # JUnit平台配置
```

## 🔧 依赖配置

### 1. Maven依赖更新

已在`app/service/pom.xml`中添加了必要的测试依赖：

```xml
<!-- JUnit 5 -->
<dependency>
    <groupId>org.junit.jupiter</groupId>
    <artifactId>junit-jupiter</artifactId>
    <scope>test</scope>
</dependency>

<!-- Mockito -->
<dependency>
    <groupId>org.mockito</groupId>
    <artifactId>mockito-core</artifactId>
    <scope>test</scope>
</dependency>

<!-- Mockito JUnit Jupiter -->
<dependency>
    <groupId>org.mockito</groupId>
    <artifactId>mockito-junit-jupiter</artifactId>
    <scope>test</scope>
</dependency>

<!-- Spring Boot Test -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-test</artifactId>
    <scope>test</scope>
</dependency>
```

### 2. 版本管理

在父`pom.xml`中添加了版本管理：

```xml
<dependency>
    <groupId>org.junit.jupiter</groupId>
    <artifactId>junit-jupiter</artifactId>
    <version>5.9.3</version>
    <scope>test</scope>
</dependency>

<dependency>
    <groupId>org.mockito</groupId>
    <artifactId>mockito-core</artifactId>
    <version>4.11.0</version>
    <scope>test</scope>
</dependency>
```

## 🧪 测试类详解

### 1. SmartChatUnitTest.java

**专门针对smartChat功能的单元测试**

#### 测试方法：

1. **`testSmartChatBasicFunctionality()`**
   - 测试基本的smartChat调用
   - 验证方法参数传递和返回值

2. **`testSmartChatIntentRecognition()`**
   - 测试意图识别功能
   - 验证不同输入对应的意图识别结果

3. **`testSmartChatParameterValidation()`**
   - 测试参数验证功能
   - 验证空参数、null参数的处理

4. **`testSmartChatContextManagement()`**
   - 测试上下文管理功能
   - 验证首次使用、状态查询、上下文清除

5. **`testSmartChatMultiLanguageSupport()`**
   - 测试多语言支持
   - 验证中英文混合输入的处理

6. **`testSmartChatErrorHandling()`**
   - 测试错误处理功能
   - 验证各种异常情况的处理

7. **`testSmartChatCompleteWorkflow()`**
   - 测试完整工作流程
   - 验证端到端的功能流程

#### 使用的Mock技术：

```java
@ExtendWith(MockitoExtension.class)
public class SmartChatUnitTest {
    
    @Mock
    private BklightMockAgentService bklightMockAgentService;
    
    // 使用when().thenReturn()模拟方法行为
    when(bklightMockAgentService.smartChatWithContext(anyString(), anyString()))
        .thenReturn("Mock response from smartChat");
    
    // 使用verify()验证方法调用
    verify(bklightMockAgentService, times(1))
        .smartChatWithContext(testUserId, "开始执行测试");
}
```

### 2. SmartChatIntegrationTest.java

**集成测试，测试与实际服务的集成**

#### 特点：
- 使用`@SpringBootTest`进行Spring上下文集成
- 测试真实的服务交互
- 包含性能基准测试
- 支持外部依赖不可用时的优雅降级

### 3. BklightMockAgentTest.java（已更新）

**原有测试类，已添加smartChat专项测试**

#### 新增测试方法：
- `testSmartChatBasicFunctionality()`
- `testSmartChatIntentRecognition()`
- `testSmartChatParameterValidation()`
- `testSmartChatContextManagement()`
- `testSmartChatStepOrderValidation()`
- `testSmartChatMultiLanguageSupport()`
- `testSmartChatComplexExpressionUnderstanding()`
- `testSmartChatErrorHandling()`
- `testSmartChatPerformanceAndConcurrency()`
- `testSmartChatCompleteWorkflow()`

## 🚀 运行测试

### 1. 命令行运行

```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=SmartChatUnitTest

# 运行特定测试方法
mvn test -Dtest=SmartChatUnitTest#testSmartChatBasicFunctionality
```

### 2. 使用脚本运行

```bash
# 给脚本执行权限
chmod +x run-smart-chat-tests.sh

# 运行测试脚本
./run-smart-chat-tests.sh
```

### 3. IDE中运行

在IntelliJ IDEA或Eclipse中：
1. 右键点击测试类
2. 选择"Run Tests"
3. 查看测试结果

## 📊 测试覆盖范围

### 功能覆盖

- ✅ **基本功能**: 方法调用、参数传递、返回值验证
- ✅ **意图识别**: 多种输入表达方式的意图识别
- ✅ **参数验证**: 空值、null值、无效值的处理
- ✅ **上下文管理**: 用户会话状态的管理
- ✅ **多语言支持**: 中英文混合输入的处理
- ✅ **错误处理**: 异常情况的优雅处理
- ✅ **完整流程**: 端到端的工作流程验证

### 场景覆盖

- ✅ **正常场景**: 标准的用户交互流程
- ✅ **异常场景**: 错误输入、系统异常等
- ✅ **边界场景**: 极值输入、特殊字符等
- ✅ **并发场景**: 多用户同时使用
- ✅ **性能场景**: 响应时间、吞吐量测试

## 🔍 测试验证点

### 1. 功能验证

```java
// 验证返回值不为空
assertNotNull(response);

// 验证返回值内容
assertTrue(response.contains("expected_content"));

// 验证方法调用次数
verify(service, times(1)).method(params);
```

### 2. 行为验证

```java
// 验证方法被正确调用
verify(bklightMockAgentService)
    .smartChatWithContext(eq(testUserId), anyString());

// 验证参数传递
verify(bklightMockAgentService)
    .smartChatWithContext(testUserId, "开始执行测试");
```

### 3. 状态验证

```java
// 验证对象状态
assertEquals(expectedValue, actualValue);

// 验证布尔条件
assertTrue(condition);
assertFalse(condition);
```

## 🛠️ 故障排查

### 1. 常见问题

**问题**: `when`方法无法解析
**解决**: 确保导入了正确的Mockito静态方法：
```java
import static org.mockito.Mockito.*;
```

**问题**: JUnit 5注解不识别
**解决**: 确保使用了正确的JUnit 5依赖和注解：
```java
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
```

**问题**: Spring上下文加载失败
**解决**: 检查测试配置文件和依赖注入：
```java
@SpringBootTest
@ActiveProfiles("test")
```

### 2. 调试技巧

1. **启用详细日志**:
   ```properties
   logging.level.com.alipay.findataquality.service.ai=DEBUG
   ```

2. **使用断点调试**:
   在IDE中设置断点，逐步调试测试逻辑

3. **查看测试报告**:
   ```bash
   mvn surefire-report:report
   ```

## 📈 测试最佳实践

### 1. 测试命名

- 使用描述性的测试方法名
- 使用`@DisplayName`注解提供中文描述
- 遵循`test + 功能名称 + 场景`的命名规范

### 2. 测试结构

- 使用AAA模式：Arrange（准备）、Act（执行）、Assert（断言）
- 每个测试方法只测试一个功能点
- 使用`@BeforeEach`进行测试数据初始化

### 3. Mock使用

- 只Mock必要的依赖
- 使用具体的参数匹配而不是`any()`
- 验证重要的方法调用

### 4. 断言策略

- 使用具体的断言而不是通用断言
- 提供有意义的断言失败消息
- 验证关键的业务逻辑

## 🎯 后续改进

1. **增加更多边界测试**
2. **添加性能基准测试**
3. **集成代码覆盖率工具**
4. **添加测试数据生成器**
5. **实现测试并行执行**

这个测试套件为SmartChat功能提供了全面的测试覆盖，确保功能的正确性和稳定性。
