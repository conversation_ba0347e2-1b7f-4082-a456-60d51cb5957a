# BKlight Agent 大模型配置指南

## 🎯 配置概述

BKlight Mock Agent 现在支持全局配置大模型参数，包括模型名称、温度设置等，方便在不同环境中灵活配置。

## 📝 配置方式

### 1. 配置文件方式

#### application.properties
```properties
# 基础配置
bklight.agent.llm.model=Qwen3-235B-A22B
bklight.agent.llm.temperature=0.1
bklight.agent.llm.intent-analysis.temperature=0.1
bklight.agent.llm.max-retry=3
bklight.agent.llm.timeout=30

# 监控和日志
bklight.agent.monitoring.enabled=true
bklight.agent.logging.llm-calls=false
bklight.agent.logging.intent-analysis=false
bklight.agent.metrics.enabled=true
```

#### application-dev.properties (开发环境)
```properties
bklight.agent.llm.model=Qwen3-72B-A14B
bklight.agent.llm.temperature=0.2
bklight.agent.llm.intent-analysis.temperature=0.1
bklight.agent.logging.llm-calls=true
bklight.agent.logging.intent-analysis=true
```

#### application-prod.properties (生产环境)
```properties
bklight.agent.llm.model=Qwen3-235B-A22B
bklight.agent.llm.temperature=0.1
bklight.agent.llm.intent-analysis.temperature=0.05
bklight.agent.llm.max-retry=5
bklight.agent.llm.timeout=60
```

### 2. 环境变量方式

```bash
# 设置模型名称
export BKLIGHT_AGENT_LLM_MODEL=GPT-4

# 设置温度
export BKLIGHT_AGENT_LLM_TEMPERATURE=0.2

# 设置意图分析温度
export BKLIGHT_AGENT_LLM_INTENT_ANALYSIS_TEMPERATURE=0.1
```

### 3. JVM参数方式

```bash
java -jar app.jar \
  -Dbklight.agent.llm.model=Claude-3 \
  -Dbklight.agent.llm.temperature=0.15 \
  -Dbklight.agent.llm.intent-analysis.temperature=0.05
```

## 🔧 动态配置管理

### 查看当前配置

```bash
# 获取完整配置
curl -X GET "http://localhost:8080/api/bklight/agent/config/current"

# 获取配置摘要
curl -X GET "http://localhost:8080/api/bklight/agent/config/summary"

# 验证配置有效性
curl -X GET "http://localhost:8080/api/bklight/agent/config/validate"
```

**响应示例**:
```json
{
  "llm": {
    "model": "Qwen3-235B-A22B",
    "temperature": 0.1,
    "maxRetry": 3,
    "timeout": 30
  },
  "intentAnalysis": {
    "temperature": 0.1,
    "cache": {
      "enabled": true,
      "expireMinutes": 60
    }
  },
  "monitoring": {
    "enabled": true
  },
  "logging": {
    "llmCalls": false,
    "intentAnalysis": false
  },
  "metrics": {
    "enabled": true
  }
}
```

### 动态修改配置

#### 更新模型名称
```bash
curl -X POST "http://localhost:8080/api/bklight/agent/config/llm/model?model=GPT-4"
```

#### 更新LLM温度
```bash
curl -X POST "http://localhost:8080/api/bklight/agent/config/llm/temperature?temperature=0.2"
```

#### 更新意图分析温度
```bash
curl -X POST "http://localhost:8080/api/bklight/agent/config/intent-analysis/temperature?temperature=0.05"
```

#### 切换监控状态
```bash
curl -X POST "http://localhost:8080/api/bklight/agent/config/monitoring/toggle"
```

#### 切换日志记录
```bash
curl -X POST "http://localhost:8080/api/bklight/agent/config/logging/llm-calls/toggle"
```

### 预设配置

#### 重置为生产环境配置
```bash
curl -X POST "http://localhost:8080/api/bklight/agent/config/reset/production"
```

#### 重置为开发环境配置
```bash
curl -X POST "http://localhost:8080/api/bklight/agent/config/reset/development"
```

## 🎨 模型选择指南

### 推荐模型配置

| 环境 | 模型 | 温度 | 意图分析温度 | 适用场景 |
|------|------|------|-------------|----------|
| 生产环境 | Qwen3-235B-A22B | 0.1 | 0.05 | 高准确性要求 |
| 测试环境 | Qwen3-72B-A14B | 0.15 | 0.1 | 平衡性能和准确性 |
| 开发环境 | Qwen3-72B-A14B | 0.2 | 0.1 | 快速迭代开发 |
| 演示环境 | Qwen3-235B-A22B | 0.3 | 0.1 | 展示效果 |

### 模型特性对比

| 模型 | 响应速度 | 准确性 | 成本 | 推荐用途 |
|------|---------|--------|------|----------|
| Qwen3-235B-A22B | 中等 | 极高 | 高 | 生产环境 |
| Qwen3-72B-A14B | 快 | 高 | 中 | 开发测试 |
| GPT-4 | 中等 | 极高 | 高 | 特殊需求 |
| Claude-3 | 中等 | 极高 | 高 | 特殊需求 |

## 🌡️ 温度参数调优

### 温度设置建议

| 用途 | 推荐温度 | 说明 |
|------|---------|------|
| 意图分析 | 0.0 - 0.1 | 需要高度确定性 |
| 工具调用 | 0.1 - 0.2 | 平衡准确性和灵活性 |
| 对话生成 | 0.2 - 0.5 | 自然对话体验 |
| 创意生成 | 0.5 - 1.0 | 需要创造性 |

### 温度对比效果

```bash
# 低温度 (0.1) - 高确定性
"用户想要开始测试" → start_new_flow (99%一致性)

# 中温度 (0.5) - 平衡性
"用户想要开始测试" → start_new_flow (90%一致性，更自然的表达)

# 高温度 (1.0) - 高创造性
"用户想要开始测试" → 可能产生更多样化的响应
```

## 📊 监控和调试

### 启用详细日志

```properties
# 启用LLM调用日志
bklight.agent.logging.llm-calls=true

# 启用意图分析日志
bklight.agent.logging.intent-analysis=true

# 设置日志级别
logging.level.com.alipay.findataquality.service.ai=DEBUG
```

### 查看运行时指标

```bash
# 查看配置状态
curl -X GET "http://localhost:8080/api/bklight/agent/config/summary"

# 查看系统健康状态
curl -X GET "http://localhost:8080/actuator/health"

# 查看指标信息
curl -X GET "http://localhost:8080/actuator/metrics"
```

## 🚀 最佳实践

### 1. 环境隔离
- 开发环境：使用较快的模型，启用详细日志
- 测试环境：使用生产级模型，适中的日志级别
- 生产环境：使用最稳定的模型，最小化日志

### 2. 性能优化
- 根据业务需求选择合适的模型
- 意图分析使用较低温度确保准确性
- 对话生成可以使用稍高温度提升体验

### 3. 配置管理
- 使用配置文件管理不同环境的设置
- 通过环境变量覆盖敏感配置
- 定期验证配置有效性

### 4. 监控告警
- 监控模型调用成功率
- 监控响应时间
- 设置配置变更告警

## 🔍 故障排查

### 常见问题

1. **模型调用失败**
   ```bash
   # 检查配置
   curl -X GET "http://localhost:8080/api/bklight/agent/config/validate"
   
   # 检查模型名称是否正确
   curl -X GET "http://localhost:8080/api/bklight/agent/config/current"
   ```

2. **意图识别不准确**
   ```bash
   # 降低意图分析温度
   curl -X POST "http://localhost:8080/api/bklight/agent/config/intent-analysis/temperature?temperature=0.05"
   ```

3. **响应速度慢**
   ```bash
   # 切换到更快的模型
   curl -X POST "http://localhost:8080/api/bklight/agent/config/llm/model?model=Qwen3-72B-A14B"
   ```

### 日志分析

```bash
# 查看LLM调用日志
tail -f logs/application.log | grep "LLM"

# 查看意图分析日志
tail -f logs/application.log | grep "Intent"

# 查看配置变更日志
tail -f logs/application.log | grep "Config"
```

这个配置系统提供了灵活的大模型管理能力，支持运行时动态调整，方便在不同场景下优化Agent性能。
